package com.tui.destilink.framework.locking.redis.lock.impl;

import com.tui.destilink.framework.locking.redis.lock.AbstractRedisLock;
import com.tui.destilink.framework.locking.redis.lock.config.RedisLockProperties;
import com.tui.destilink.framework.locking.redis.lock.exception.LockAcquisitionException;
import com.tui.destilink.framework.locking.redis.lock.exception.LockReleaseException;
import com.tui.destilink.framework.locking.redis.lock.service.LockOwnerSupplier;
import com.tui.destilink.framework.locking.redis.lock.service.LockWatchdog;
import com.tui.destilink.framework.locking.redis.lock.service.RedisLockOperations;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;

/**
 * Redis-based implementation of a Read Lock, part of a ReadWriteLock pair.
 * <p>
 * This lock allows multiple readers to acquire the lock concurrently as long as
 * no writer holds the lock. It supports reentrancy and is implemented using
 * Redis operations for distributed locking.
 * </p>
 */
@Slf4j
public class RedisReadLock extends AbstractRedisLock {

    public static final String LOCK_MODE = "READ";
    public static final String READ_LOCK_TYPE = "RedisReadLock";

    /**
     * Constructs a RedisReadLock.
     *
     * @param redisLockOperations Service for Redis lock operations.
     * @param lockOwnerSupplier   Supplier for the lock owner ID.
     * @param properties          Configuration properties for Redis locks.
     * @param lockKey             The unique key for the parent ReadWriteLock in
     *                            Redis.
     * @param lockTtlMillis       Time-to-live for the lock in milliseconds.
     * @param retryIntervalMillis Interval between retry attempts in milliseconds.
     * @param maxRetries          Maximum number of retry attempts.
     */
    public RedisReadLock(
            RedisLockOperations redisLockOperations,
            LockOwnerSupplier lockOwnerSupplier,
            RedisLockProperties properties,
            String lockKey, // This is the main ReadWriteLock key
            long lockTtlMillis,
            long retryIntervalMillis,
            int maxRetries,
            ExecutorService virtualThreadExecutor,
            LockWatchdog watchdog) {
        super(redisLockOperations, lockOwnerSupplier, properties, lockKey,
                lockTtlMillis, retryIntervalMillis, maxRetries, virtualThreadExecutor, watchdog);
    }

    @Override
    protected String getLockType() {
        return READ_LOCK_TYPE;
    }

    @Override
    protected CompletableFuture<Boolean> doTryLock(String ownerId, Duration effectiveTimeout) {
        log.debug("Attempting to acquire read lock (doTryLock): lockKey={}, ownerId={}, timeout={}", getLockKey(),
                ownerId, effectiveTimeout);
        return redisLockOperations.tryReadLock(
                getLockKey(),
                getResponseCacheKey(),
                null, // UUID generated internally by RedisLockOperationsImpl
                String.valueOf(effectiveTimeout.toMillis()),
                ownerId,
                String.valueOf(getResponseCacheTtlSeconds().getSeconds())).thenApply(result -> {
                    boolean acquired = result != null && result > 0;
                    log.debug("Read lock tryLock {}: lockKey={}, ownerId={}", acquired ? "successful" : "failed",
                            getLockKey(), ownerId);
                    return acquired;
                });
    }

    @Override
    protected CompletableFuture<Void> doUnlock(String ownerId) {
        log.debug("Attempting to release read lock: lockKey={}, ownerId={}", getLockKey(), ownerId);
        return redisLockOperations.unlockReadLock(getLockKey(), ownerId, null) // UUID generated internally
                .thenAccept(result -> {
                    if ("OK".equals(result)) {
                        log.debug("Read lock released: lockKey={}, ownerId={}", getLockKey(), ownerId);
                    } else {
                        log.warn("Failed to release read lock: lockKey={}, ownerId={}, result={}", getLockKey(),
                                ownerId, result);
                        throw new LockReleaseException(getLockKey(), getLockType(), ownerId,
                                "Failed to release read lock");
                    }
                });
    }

    @Override
    public boolean isReadLock() {
        return true;
    }

    @Override
    public boolean isWriteLock() {
        return false;
    }
}
