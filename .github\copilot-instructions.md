### Destilink Framework AI Coding Assistant Guide

This document provides essential guidelines for AI-powered development within the Destilink Framework. Adhering to these instructions is critical for maintaining code quality, consistency, and stability.

#### Core Principles & Architecture

*   **Framework Overview**: Destilink is an opinionated abstraction layer over Spring Boot, designed to standardize and accelerate the development of microservices and cronjobs.
*   **Asynchronous-First Design**: The framework, particularly the `locking-redis-lock` module, is built on an async-first model using Java's `CompletableFuture` and Virtual Threads for non-blocking operations.
*   **Redis for Distributed Locking**: The `locking-redis-lock` module provides robust distributed locking capabilities.
    *   **Atomicity**: All Redis operations that modify state are executed as atomic Lua scripts.
    *   **Idempotency**: Mutating operations are idempotent, managed centrally by `RedisLockOperationsImpl` which generates a `requestUuid` for each logical operation.
    *   **Watchdog**: A `LockWatchdog` service automatically extends the lease of active locks to prevent premature expiration during long-running tasks.
    *   **Key Schema**: Redis keys follow a strict, semantically isolated schema: `<prefix>:<bucketName>:__locks__:<lockType>:{<lockName>}`.

#### Build & Test Commands

*   **Full Build**: `mvn clean install`
*   **Run All Tests**: `mvn test`
*   **Run a Single Test**: `mvn test -Dtest=com.tui.destilink.framework.locking.redis.lock.impl.RedisStampedLockTest`
*   **Build Documentation**: `mkdocs build` (uses `mkdocs.yml`)

#### Critical Coding Rules

*   **NO COMPONENT SCANNING**: Modules **MUST NOT** use `@ComponentScan`. This is the most critical rule. All beans must be loaded explicitly via `@Import` statements in `@AutoConfiguration` classes or defined in `@Bean` methods.
*   **Conditional Beans**: Every `@Bean` definition **MUST** include at least one conditional annotation (e.g., `@ConditionalOnProperty`, `@ConditionalOnClass`, `@ConditionalOnMissingBean`). Unconditional beans are strictly prohibited.
*   **Constructor Injection ONLY**: Use constructor injection with `final` fields for all dependencies. `@Autowired` on fields is forbidden.
*   **Dependency Management**:
    *   Before adding a new dependency, you **MUST** verify it is not already available as a transitive dependency by running `mvn dependency:tree`.
    *   Do not add Spring Boot starters that are already included transitively via `ms-core` or `cronjob-core`.
    *   Versions are managed in the parent POMs; do not specify versions in module `pom.xml` files unless the dependency is exclusive to that module.
*   **Testing**:
    *   All tests **MUST** use the provided `test-support` modules (e.g., `test-core`, `redis-test-support`).
    *   **DO NOT** use standard Spring Boot test dependencies like `spring-boot-starter-test` directly.
    *   Integration tests should leverage the `@RedisTestSupport`, `@KeycloakTestSupport`, etc., annotations.
*   **Naming Conventions**:
    *   **Packages**: `com.tui.destilink.framework.<module>[.<submodule>]`
    *   **Auto-Configuration Classes**: `<Feature>AutoConfiguration` (e.g., `RedisLockAutoConfiguration`).
    *   **Test Classes**: Unit tests end with `*Test`, integration tests end with `*IT`.
*   **Error Handling**:
    *   Use the framework's base exceptions like `MarkerNestedRuntimeException`.
    *   Web errors should be handled by `@ControllerAdvice` classes that return `ProblemDetail` (RFC 7807).
*   **Logging**:
    *   Use `@Slf4j` for logging.
    *   Use context decorators that extend `AbstractContextDecorator<T>` to add structured information to the MDC.
