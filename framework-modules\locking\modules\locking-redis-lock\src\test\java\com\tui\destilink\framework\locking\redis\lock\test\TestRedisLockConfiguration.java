package com.tui.destilink.framework.locking.redis.lock.test;

import com.tui.destilink.framework.locking.redis.lock.config.RedisLockProperties;
import com.tui.destilink.framework.locking.redis.lock.service.RedisLockErrorHandler;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;

import java.time.Duration;

/**
 * Test configuration that provides real Redis components for testing.
 * This configuration replaces the mock-based implementation with a real Redis implementation
 * that uses the embedded Redis server provided by the redis-test-support module.
 */
@TestConfiguration
public class TestRedisLockConfiguration {

    /**
     * Creates default Redis lock properties for testing if not already provided.
     * These properties use shorter timeouts suitable for testing.
     *
     * @return Redis lock properties configured for testing
     */
    @Bean
    @Primary
    public RedisLockProperties testRedisLockProperties() {
        // Create test-specific properties with shorter timeouts
        RedisLockProperties properties = new RedisLockProperties();
        properties.setEnabled(true);
        properties.setStateKeyExpiration(Duration.ofSeconds(5));
        properties.setDefaults(createDefaults());
        properties.setWatchdog(createWatchdogProperties());
        properties.setResponseCacheTtl(Duration.ofSeconds(10));
        properties.setRetry(createRetryConfig());
        return properties;
    }

    /**
     * Creates default lock properties for testing.
     *
     * @return Default lock properties for testing
     */
    private RedisLockProperties.Defaults createDefaults() {
        RedisLockProperties.Defaults defaults = new RedisLockProperties.Defaults();
        defaults.setLeaseTime(Duration.ofSeconds(5));
        defaults.setRetryInterval(Duration.ofMillis(50));
        defaults.setMaxRetries(2);
        defaults.setAcquireTimeout(Duration.ofSeconds(2));
        return defaults;
    }

    /**
     * Creates watchdog properties for testing.
     *
     * @return Watchdog properties for testing
     */
    private RedisLockProperties.WatchdogProperties createWatchdogProperties() {
        RedisLockProperties.WatchdogProperties watchdog = new RedisLockProperties.WatchdogProperties();
        watchdog.setInterval(Duration.ofSeconds(1));
        watchdog.setFactor(0.5);
        watchdog.setCorePoolSize(1);
        watchdog.setThreadNamePrefix("test-redis-lock-watchdog-");
        watchdog.setShutdownAwaitTermination(Duration.ofSeconds(2));
        return watchdog;
    }

    /**
     * Creates retry configuration for testing.
     *
     * @return Retry configuration for testing
     */
    private RedisLockProperties.RetryConfig createRetryConfig() {
        RedisLockProperties.RetryConfig retry = new RedisLockProperties.RetryConfig();
        retry.setMaxAttempts(3);
        retry.setInitialDelay(Duration.ofMillis(50));
        retry.setBackoffMultiplier(2.0);
        retry.setMaxDelay(Duration.ofSeconds(1));
        retry.setJitterEnabled(true);
        retry.setCircuitBreakerFailureThreshold(3);
        retry.setCircuitBreakerRecoveryTimeout(Duration.ofSeconds(5));
        return retry;
    }

    /**
     * Creates a Redis lock error handler for testing.
     *
     * @return A Redis lock error handler
     */
    @Bean
    @Primary
    public RedisLockErrorHandler testRedisLockErrorHandler() {
        return new RedisLockErrorHandler();
    }
}
