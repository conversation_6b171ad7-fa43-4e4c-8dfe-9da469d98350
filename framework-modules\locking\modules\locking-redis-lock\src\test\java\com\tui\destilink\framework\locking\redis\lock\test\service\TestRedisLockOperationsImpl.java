package com.tui.destilink.framework.locking.redis.lock.test.service;

import com.tui.destilink.framework.locking.redis.lock.config.RedisLockProperties;
import com.tui.destilink.framework.locking.redis.lock.service.RedisLockErrorHandler;
import com.tui.destilink.framework.locking.redis.lock.service.RedisLockOperations;
import io.lettuce.core.cluster.api.StatefulRedisClusterConnection;

import java.time.Duration;
import java.util.concurrent.CompletableFuture;

public class TestRedisLockOperationsImpl implements RedisLockOperations {

    private final StatefulRedisClusterConnection<String, String> connection;
    private final RedisLockProperties properties;
    private final RedisLockErrorHandler errorHandler;

    public TestRedisLockOperationsImpl(
            StatefulRedisClusterConnection<String, String> connection,
            RedisLockProperties properties,
            RedisLockErrorHandler errorHandler) {
        this.connection = connection;
        this.properties = properties;
        this.errorHandler = errorHandler;
    }

    @Override
    public CompletableFuture<Boolean> tryLock(String lockKey, String ownerId, Duration ttl, Duration acquireTimeout) {
        return CompletableFuture.completedFuture(true);
    }

    @Override
    public CompletableFuture<Void> unlock(String lockKey, String ownerId) {
        return CompletableFuture.completedFuture(null);
    }

    @Override
    public CompletableFuture<Boolean> extendLock(String lockKey, String ownerId, Duration ttl) {
        return CompletableFuture.completedFuture(true);
    }

    @Override
    public CompletableFuture<Boolean> isLocked(String lockKey) {
        return CompletableFuture.completedFuture(false);
    }

    @Override
    public CompletableFuture<Long> refreshLockByWatchdog(String lockKey, String ownerId, long targetExpiresAtMillis) {
        return CompletableFuture.completedFuture(1L);
    }

    @Override
    public CompletableFuture<String> hget(String key, String field) {
        return CompletableFuture.completedFuture(null);
    }

    @Override
    public CompletableFuture<Long> getTtl(String lockKey) {
        return CompletableFuture.completedFuture(-2L);
    }

    @Override
    public CompletableFuture<Boolean> acquireLock(String lockKey, String ownerId, Duration ttl) {
        return CompletableFuture.completedFuture(true);
    }

    @Override
    public CompletableFuture<Boolean> releaseLock(String lockKey, String ownerId) {
        return CompletableFuture.completedFuture(true);
    }

    @Override
    public CompletableFuture<Boolean> checkLock(String lockKey, String ownerId) {
        return CompletableFuture.completedFuture(true);
    }

    @Override
    public CompletableFuture<String> getString(String key) {
        return CompletableFuture.completedFuture(null);
    }

    @Override
    public CompletableFuture<Boolean> updateState(String lockKey, String ownerId, String requestUuid, String newState, String stateKey, String stateExpiration, String stateExpirationMs, String responseCacheKey) {
        return CompletableFuture.completedFuture(true);
    }

    @Override
    public CompletableFuture<Boolean> updateStateIfEquals(String lockKey, String ownerId, String requestUuid, String expectedState, String newState, String stateKey, String stateExpiration, String stateExpirationMs, String responseCacheKey) {
        return CompletableFuture.completedFuture(true);
    }

    @Override
    public CompletableFuture<Boolean> tryStateLock(String lockKey, String ownerId, String requestUuid, String initialState, String stateKey, String lockTtl, boolean allowExisting, String stateExpiration, String responseCacheKey) {
        return CompletableFuture.completedFuture(true);
    }

    @Override
    public CompletableFuture<Boolean> unlockStateLock(String lockKey, String ownerId, String newState, String requestUuid, String stateKey, String stateExpiration, String stateExpirationMs, String unlockCommand, String responseCacheKey) {
        return CompletableFuture.completedFuture(true);
    }

    @Override
    public CompletableFuture<String> getStateLockState(String stateKey) {
        return CompletableFuture.completedFuture(null);
    }

    @Override
    public CompletableFuture<Long> tryWriteLock(String lockKey, String requestUuid, String responseCacheKey, String lockTtl, String ownerId, String stampedDataKey, String stampedLockMode) {
        return CompletableFuture.completedFuture(1L);
    }

    @Override
    public CompletableFuture<Long> tryReadLock(String lockKey, String requestUuid, String responseCacheKey, String lockTtl, String ownerId, String stampedDataKey) {
        return CompletableFuture.completedFuture(1L);
    }

    @Override
    public CompletableFuture<Boolean> unlockWriteLock(String lockKey, String ownerId, String stamp, String unlockCommand) {
        return CompletableFuture.completedFuture(true);
    }

    @Override
    public CompletableFuture<Boolean> unlockReadLock(String lockKey, String ownerId, String stamp) {
        return CompletableFuture.completedFuture(true);
    }
}
