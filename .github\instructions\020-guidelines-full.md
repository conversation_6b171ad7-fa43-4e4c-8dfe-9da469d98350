# Project and Coding Guidelines For The Destilink Framework

These guidelines MUST be strictly followed by automated coding agents and LLMs developing for the Destilink Framework.

***

```yaml
# METADATA
framework: Destilink Framework
type: Spring Boot abstraction
organization: TUI
purpose: Standardize and accelerate microservice/cronjob development
philosophy: Plug-n-play, opinionated solutions for cross-cutting concerns
core_principles:
  - Consistency
  - Maintainability
  - Testability
  - Operational Excellence
  - Opinionated Design
```

## Project Structure Map

```yaml
# ROOT STRUCTURE
root_directory: destilink-framework/
critical_files:
  - pom.xml: Top-level Parent POM - DO NOT MODIFY
  - .gitlab-ci.yml: CI Pipeline definition
  - lombok.config: Lombok configuration
  - .mvn/: Maven wrapper configuration

# PRIMARY DIRECTORIES
primary_directories:
  - framework-dependencies-parent: Central dependency version management - MODIFY CAREFULLY
  - framework-bom: Bill of Materials for recommended dependencies - MODIFY CAREFULLY
  - framework-build-parent: Common build config - DO NOT MODIFY DIRECTLY
  - framework-build-parent-ms: Microservice builds - DO NOT MODIFY DIRECTLY
  - framework-build-parent-cronjob: Cronjob builds - DO NOT MODIFY DIRECTLY
  - framework-modules: FUNCTIONAL CODE - PRIMARY DEVELOPMENT AREA
  - framework-test-applications: Integration test applications
  - framework-aggregated-report: CI reports - DO NOT MODIFY
  - docs: Framework documentation
  - utils: Test infrastructure (Docker Compose)
```

## Naming Conventions

```yaml
# MODULE NAMING
modules: lowercase-with-hyphens
functional_areas: single-word (web, aws, locking)
sub_modules: area-subarea (web-core, aws-sqs)

# PACKAGE NAMING
base_package: com.tui.destilink.framework.<module>[.<submodule>]

# CLASS NAMING PATTERNS
singleton_classes: <Domain>ContextDecorator, <Feature>Registry
utility_classes: <Domain>Utils
factory_classes: <Technology>Factory
service_interfaces: <Domain>Service
service_implementations: <Technology><ServiceInterface>
configuration_classes: <Feature>Config, <Feature>Properties
test_classes:
  unit_tests: "*Test"
  integration_tests: "*IT"
```

## Package Structure

```yaml
# STANDARD PACKAGE STRUCTURE
com.tui.destilink.framework.<module>[.<submodule>]/
  config/: Configuration classes and properties
  service/: Business logic and service implementations
  exception/: Custom exception classes
  util/: Utility classes and helper methods
  model/: Data transfer objects, entities
  event/: Event classes
  client/: Client implementations
  annotations/: Custom annotations
  impl/: Internal implementation details (not for direct use)

# EXTENDED PACKAGE STRUCTURE
  logging/:
    context/: Context decorators and MDC utilities
    marker/: Custom log markers
    filter/: Log filtering mechanisms
    customize/: Extension points for logging customization
  validation/:
    util/: Validation utilities
    constraint/: Custom validation constraints
  http/:
    filter/: HTTP filters
    interceptor/: HTTP interceptors
    client/: HTTP client configurations
```

## Class Design Patterns

```yaml
# METADATA
# framework: Destilink Framework
# ...existing code...
# SINGLETON PATTERN
pattern: Singleton via static INSTANCE field
implementation: private constructor + static getInstance() method
examples: TripsContextDecorator, LockContextDecorator
usage: For stateless utility classes that need to be accessed globally
note: This refers to the classic Java singleton pattern, suitable for stateless utilities. These are typically not managed as Spring singleton-scoped beans (even though Spring's default bean scope is also 'singleton'). Use this pattern when Spring bean management is not necessary or desired for the utility.

# UTILITY CLASSES
pattern: Non-instantiable utility classes
annotations:
  - @UtilityClass (Lombok)
  - @NoArgsConstructor(access = AccessLevel.PRIVATE)
examples: FutureUtils, CleanerUtils, ValidationUtils, LoggingUtils
naming_convention: plural noun + "Utils" suffix

# DECORATOR PATTERN
pattern: Context decorators for structured logging
base_class: AbstractContextDecorator<T>
naming_convention: Domain + "ContextDecorator"
implementation: Extends AbstractContextDecorator with typed properties

# FACTORY PATTERN
pattern: Client factories for external services
naming_convention: Technology + "Factory" (e.g., HttpClientFactory)
implementation: Builder pattern with fluent API
```

## Method Naming Conventions

```yaml
# BUILDER METHODS
prefix: build + noun
return_type: The built object
examples: buildStringProperty(), buildUUIDProperty()

# GETTER UTILITY METHODS
prefix: get + noun
examples: getPrefix(), getBucketName(), getObjectName()

# VALIDATION METHODS
prefix: validate + object type
examples: validateObjectThrowing()

# LIMITING/TRUNCATING METHODS
prefix: limit + preposition + unit
examples: limitToBytes()
```

## Configuration Rules

```yaml
# PROPERTY NAMING
property_prefix: destilink.fw
property_pattern: destilink.fw.<functional-area>.<module>[.<sub-module>].<property-name>
property_style: kebab-case
examples:
  - destilink.fw.web.core.error-responses.use-problem-details
  - destilink.fw.aws.sqs.logging.enabled

# CONFIGURATION FILES
config_file_pattern: NNNN-<module>-<optional-descriptor>.application.yml
load_order: NNNN (framework defaults use 1000-)
environment_specific:
  - test: .test.yml
  - microservice: .ms.yml
  - cronjob: .cronjob.yml

# CONFIGURATION CLASSES
annotations:
  - @ConfigurationProperties(prefix = "...")
  - @Validated
  - @NotNull, @NotBlank, @Pattern, @Min, @Max, @Valid
best_practices:
  - Provide sensible defaults
  - Use nested static classes for hierarchical properties
  - Add Javadoc for each property

# AUTO-CONFIGURATION
annotations:
  - @Configuration
  - @AutoConfiguration (REQUIRED as the primary annotation for module entry point)
  - @EnableConfigurationProperties
  - @ConditionalOn... (@ConditionalOnClass, @ConditionalOnProperty, @ConditionalOnBean, @ConditionalOnMissingBean)
registration: src/main/resources/META-INF/spring/org.springframework.boot.autoconfigure.AutoConfiguration.imports
metadata: src/main/resources/META-INF/additional-spring-configuration-metadata.json

# MODULE AUTOCONFIGURATION RULES
mandatory_rules:
  - Modules MUST be configured via `@AutoConfiguration` classes, not regular @Configuration
      - VIOLATION: Using @Configuration instead of @AutoConfiguration for module entry points
      - VIOLATION: Missing @AutoConfiguration annotation on primary configuration class

  - Modules MUST NEVER use `@ComponentScan` under ANY circumstances
      - VIOLATION: Including ANY form of component scanning, even with restrictions
      - VIOLATION: Using @SpringBootApplication with default component scanning
      - CRITICAL: Component scanning is ABSOLUTELY PROHIBITED - NO EXCEPTIONS

  - `@Import` annotation MUST be used for ALL component loading
      - REQUIRED: Every single component MUST be explicitly imported
      - REQUIRED: Component graphs MUST be explicitly defined, not discovered
      - VIOLATION: Relying on package structure for component discovery

  - Beans MUST ALWAYS be registered explicitly via `@Bean` methods
      - REQUIRED: Every bean must have a clear, traceable registration point
      - REQUIRED: Factory methods should have clear naming conventions
      - VIOLATION: Relying on stereotype annotations without explicit imports

  - Registration in META-INF is MANDATORY
      - REQUIRED: All auto-configuration classes MUST be registered in:
        META-INF/spring/org.springframework.boot.autoconfigure.AutoConfiguration.imports
      - VIOLATION: Missing registration of ANY auto-configuration class
      - VIOLATION: Using deprecated spring.factories registration mechanism

  - Package isolation MUST be maintained
      - REQUIRED: Auto-configuration classes MUST reside in a dedicated 'config' package
      - REQUIRED: Keep clear separation between configuration and components
      - VIOLATION: Mixing configuration classes with component implementations
```

### WHY @COMPONENTSCAN IS ABSOLUTELY PROHIBITED IN MODULES

#### CATASTROPHIC FAILURES CAUSED BY COMPONENT SCANNING

1. **CRITICAL LIFECYCLE VIOLATION**
   * **Root Issue**: Auto-configuration operates at a distinct phase in Spring Boot's startup lifecycle
   * **Violation**: @ComponentScan forces beans into WRONG lifecycle phase
   * **Consequence**: Bean initialization order becomes UNPREDICTABLE and CORRUPTED
   * **Failure Mode**: Catastrophic initialization errors that appear ONLY in certain environments
   * **Production Impact**: Random startup failures that are nearly impossible to diagnose

2. **FATAL DEPENDENCY CORRUPTION**
   * **Root Issue**: Auto-configuration enforces explicit conditional bean creation
   * **Violation**: Component scanning bypasses ALL conditional guards
   * **Consequence**: Wrong beans load in wrong order with wrong dependencies
   * **Failure Mode**: Apparent "random" Spring context initialization failures
   * **Production Impact**: Application crashes with cryptic dependency error messages

3. **UNTRACEABLE SERVICE FAILURES**
   * **Root Issue**: Component scanning creates implicit, non-auditable dependency paths
   * **Violation**: Dependencies become invisible to static analysis
   * **Consequence**: Impossible to debug configuration issues
   * **Failure Mode**: Services available in development but NOT in production
   * **Production Impact**: Mysterious service unavailability without clear error logs

4. **ENVIRONMENT-SPECIFIC FAILURES**
   * **Root Issue**: Package structure scanning behavior varies by environment
   * **Violation**: Components load differently based on classpath specifics
   * **Consequence**: Tests pass locally but fail in CI/CD or production
   * **Failure Mode**: "Works on my machine" syndrome with no clear cause
   * **Production Impact**: Deployment pipeline failures with inconsistent behavior

5. **UNINTENDED BEAN OVERRIDES**
   * **Root Issue**: Component scanning registers beans with unpredictable priority
   * **Violation**: Scanned beans may replace intended configuration beans
   * **Consequence**: Silent overriding of critical infrastructure components
   * **Failure Mode**: Subtle behavioral differences between environments
   * **Production Impact**: Data corruption or security vulnerabilities

#### ZERO TOLERANCE POLICY

* Component scanning in module code will be AUTOMATICALLY REJECTED by CI/CD pipelines
* Code reviews MUST flag and block ANY form of component scanning
* NO EXCEPTIONS are permitted for ANY reason - alternatives ALWAYS exist
* ANY team member may escalate component scanning attempts to architecture review
* Educational resources and refactoring assistance will be provided

#### REQUIRED ALTERNATIVES

1. Use explicit `@Import` statements for ALL component registration
2. Configure beans via `@Bean` factory methods with clear documentation
3. Group related components in configuration classes for readability
4. Use Spring Boot's auto-configuration mechanisms properly
5. Maintain clear, traceable component dependencies

### COMPONENT LOADING PATTERNS

```yaml
mandatory:
  - Explicit `@Bean` definitions with clear conditional activation rules
      - REQUIRED: Every bean must have its own dedicated @Bean method
      - REQUIRED: Method name must match bean type camelCase name (e.g., redisTemplate() for RedisTemplate)
      - REQUIRED: Each bean must specify at least one clear condition for activation
      - VIOLATION: Bean methods without conditions or with unclear naming

  - Hierarchical configuration with nested classes
      - REQUIRED: Group related beans in nested @Configuration classes under main @AutoConfiguration
      - REQUIRED: Apply class-level conditions to nested configuration classes
      - REQUIRED: Maintain clear hierarchy of configuration components
      - RECOMMENDED: Use `proxyBeanMethods = false` on nested `@Configuration` classes (and generally any `@Configuration` class not requiring inter-bean calls to be proxied). This improves performance by bypassing CGLIB proxying and is the standard unless proxying is explicitly needed for specific inter-bean call behavior within that configuration class.
      - VIOLATION: Flat structure with unrelated beans all at top level

  - Proper dependency management
      - REQUIRED: Use ObjectProvider<T> for ALL optional dependencies
      - REQUIRED: Use constructor injection ONLY (no @Autowired fields)
      - REQUIRED: Parameters must be clearly named to indicate purpose
      - VIOLATION: Direct @Autowired fields causing hidden dependencies and testing difficulty

  - Strict separation of concerns
      - REQUIRED: Auto-configuration (framework) and application beans MUST be separated
      - REQUIRED: Configuration classes must focus on a single technical domain
      - REQUIRED: Use explicit ordering annotations (@AutoConfigureBefore/@AutoConfigureAfter) for dependencies
      - VIOLATION: Mixing concerns across configuration boundaries
```

### ABSOLUTELY PROHIBITED PRACTICES

```yaml
strictly_forbidden:
  - COMPONENT SCANNING
      - NEVER use `@ComponentScan` in module autoconfiguration - NO EXCEPTIONS
      - NEVER use `@SpringBootApplication` in framework modules
      - NEVER rely on package structure for component discovery
      - CONSEQUENCE: Unpredictable bean loading and runtime failures

  - AUTOWIRING ANTI-PATTERNS
      - NEVER use direct `@Autowired` on fields in configuration classes
      - NEVER use `@Value` on fields in configuration classes
      - NEVER mix constructor and field injection in the same class
      - CONSEQUENCE: Hidden dependencies, testing difficulties, unclear initialization order

  - BEAN METHOD VIOLATIONS
      - NEVER define multiple unrelated functionality in a single @Bean method
      - NEVER create conditional beans without specifying fallback behavior
      - NEVER use side effects in @Bean methods (they must be pure factory methods)
      - CONSEQUENCE: Complex debugging, unclear dependencies, and erratic behavior

  - CONFIGURATION MIXING
      - NEVER mix auto-configuration and user configuration concerns in same class
      - NEVER override framework beans unintentionally
      - NEVER bypass conditional mechanisms
      - CONSEQUENCE: Configuration corruption, overrides, and unpredictable behavior
```

### CONDITIONAL ACTIVATION

```yaml
requirements:
  - EVERY bean definition MUST include at least one conditional annotation
      - VIOLATION: Bean definitions without explicit conditional rules
      - CRITICAL: Unconditional beans are STRICTLY PROHIBITED
      - CLARIFICATION: This rule applies universally to ALL beans, including core framework beans or beans that seem non-optional. For such beans, a common approach is to use `@ConditionalOnProperty` with `matchIfMissing = true` to allow them to be enabled by default but still provide a configuration path to disable them if absolutely necessary.

  - PROPER USAGE OF CONDITIONALS IS MANDATORY:
      - `@ConditionalOnProperty`:
          - REQUIRED: Must specify complete prefix, name, havingValue, and matchIfMissing
          - REQUIRED: Property names MUST follow destilink.fw.<module> pattern
          - EXAMPLE: @ConditionalOnProperty(prefix = "destilink.fw.mymodule",
                     name = "enabled", havingValue = "true", matchIfMissing = true)
          - VIOLATION: Missing prefix or using default matchIfMissing

      - `@ConditionalOnClass`:
          - REQUIRED: Use for EVERY optional dependency
          - REQUIRED: Apply at configuration class level, not just bean method
          - EXAMPLE: @ConditionalOnClass(RedisConnectionFactory.class)
          - VIOLATION: Not checking for required classes before using them

      - `@ConditionalOnMissingBean`:
          - REQUIRED: Every service implementation MUST use this
          - REQUIRED: Must specify exact type or value() attribute
          - EXAMPLE: @ConditionalOnMissingBean(LockService.class)
          - VIOLATION: Custom implementations cannot be overridden

      - `@ConditionalOnBean`:
          - REQUIRED: Use for beans that depend on optional features
          - REQUIRED: Must specify the type or value() attribute
          - EXAMPLE: @ConditionalOnBean(DataSource.class)
          - VIOLATION: Creating beans that will fail at runtime if dependencies missing

  - COMPOSITIONAL CONDITIONALS MUST BE PROPERLY LAYERED
      - REQUIRED: Nest configuration classes with appropriate conditions
      - REQUIRED: Apply more specific conditions at lower levels
      - VIOLATION: Complex conditions all at one level
```

### MODULE COMPONENT ANNOTATIONS USAGE

```yaml
component_annotations:
  - STEREOTYPE ANNOTATION POLICY:
      - `@Component`, `@Service`, `@Repository` annotations require STRICT COMPLIANCE:
          - MANDATORY: These annotations are ONLY allowed with explicit `@Import`
          - FORBIDDEN: NEVER rely on component scanning to discover these beans
          - FORBIDDEN: NEVER place stereotyped components in @ComponentScan-able packages
          - CRITICAL: ALL stereotyped components MUST be explicitly imported

  - EXPLICIT LOADING REQUIREMENTS:
      - MANDATORY: EVERY component MUST be loaded through ONE of:
          1. Direct `@Import` in a configuration class
          2. `@Import` of a container configuration class
          3. Explicit `@Bean` definition method
      - VIOLATION: ANY component not explicitly imported
      - VIOLATION: ANY component discovered through scanning
      - VIOLATION: Components in unexpected package structure

  - BEAN VS STEREOTYPE PRIORITIZATION:
      - PREFERRED: Use `@Bean` definitions for ALL framework components
      - ALLOWED: Use stereotypes ONLY for complex components with many dependencies
      - FORBIDDEN: Mixed approach within a single functional area
      - REQUIRED DOCUMENTATION: When stereotypes are used, document WHY in comments

  - PACKAGE STRUCTURE ENFORCEMENT:
      - MANDATORY: Stereotyped classes MUST be in packages that match their role
          - Services in `.service` packages
          - Repositories in `.repository` packages
          - Infrastructure components in `.infrastructure` packages
      - REQUIRED: Package structure MUST reflect component purpose
      - VIOLATION: Components in incorrect or generic packages
      - VIOLATION: Poor package organization diluting component intent

import_pattern:
  - STRICT IMPORT HIERARCHY REQUIREMENTS:
      - MANDATORY: Use `@Import` to explicitly define ALL component relationships
          - REQUIRED: `@Import({ServiceA.class, ServiceB.class})` for direct component imports
          - REQUIRED: Every imported class MUST be documented with its purpose
          - VIOLATION: Any component not explicitly imported
          - VIOLATION: Relying on package structure for discovery

      - HIERARCHICAL IMPORT ORGANIZATION:
          - REQUIRED: Group related components in dedicated configuration classes
          - REQUIRED: Follow domain-driven organization of imports
          - REQUIRED: Maximum of 10 imports per configuration class
          - VIOLATION: Flat, unstructured import lists exceeding 10 components

      - MODULAR IMPORT STRUCTURE PATTERN:
          - REQUIRED: For larger sets of components, use dedicated configuration classes:
          ```java
          @Configuration
          @Import({
              // REPOSITORY LAYER
              UserRepository.class,
              ProductRepository.class,

              // SERVICE LAYER
              UserService.class,
              ProductService.class,

              // INFRASTRUCTURE
              CacheManager.class,
              MetricsCollector.class
          })
          public class ModuleComponentsConfiguration {
              // May contain additional @Bean definitions if needed
              // MUST include detailed JavaDoc explaining component responsibilities
          }
          ```
          - REQUIRED: Then import this configuration in main AutoConfiguration:
          ```java
          @AutoConfiguration
          @Import(ModuleComponentsConfiguration.class)
          public class MyModuleAutoConfiguration {
              // Core configuration and beans
          }
          ```
          - REQUIRED: Document dependencies between imported components
          - VIOLATION: Missing documentation of component relationships
          - VIOLATION: Circular import dependencies

      - IMPORT VALIDATION RULES:
          - MANDATORY: Verify all imported classes exist and are correct types
          - MANDATORY: Use static imports (MyClass.class) NEVER string-based imports
          - REQUIRED: Validate imports with compiler, never rely on runtime discovery
          - VIOLATION: Dead imports or classes that cannot be found
```

### AUTOCONFIGURATION EXAMPLE PATTERN - MANDATORY TEMPLATE

#### REQUIRED STRUCTURE

This pattern MUST be followed exactly for ALL module auto-configurations:

```java
/**
 * Auto-configuration for MyModule functionality.
 * <p>
 * MANDATORY comprehensive JavaDoc explaining:
 * 1. Overall purpose of this module
 * 2. Primary features and components
 * 3. Configuration options and default settings
 * 4. Integration points with other modules
 * 5. Usage examples
 * </p>
 *
 * <AUTHOR> Name
 */
@AutoConfiguration  // MANDATORY - NEVER use @Configuration here
@EnableConfigurationProperties(MyModuleProperties.class)  // REQUIRED for properties
@ConditionalOnProperty(
    prefix = "destilink.fw.mymodule",
    name = "enabled",
    havingValue = "true",
    matchIfMissing = true)  // REQUIRED feature toggle
public class MyModuleAutoConfiguration {

    // ==========================================
    // CORE BEANS - Primary functionality
    // ==========================================

    /**
     * Creates the primary service for MyModule functionality.
     * <p>
     * REQUIRED detailed documentation explaining:
     * - Purpose and responsibility of this bean
     * - Configuration options affecting behavior
     * - Why @ConditionalOnMissingBean is used (allows override)
     * </p>
     *
     * @param properties Module configuration properties
     * @return Configured service instance
     */
    @Bean
    @ConditionalOnMissingBean  // MANDATORY - allows custom implementations
    public MyService myService(MyModuleProperties properties) {
        // REQUIRED: All initialization parameters must be clearly documented
        // REQUIRED: No side effects or external calls in bean creation
        return new MyServiceImpl(properties.getConfig());
    }

    // ==========================================
    // OPTIONAL FEATURES - Conditionally activated
    // ==========================================

    /**
     * Optional configuration activated when specific classes are available.
     * <p>
     * REQUIRED documentation explaining activation conditions and dependencies
     * </p>
     */
    @Configuration(proxyBeanMethods = false)  // REQUIRED: false for performance
    @ConditionalOnClass(OptionalDependency.class)  // REQUIRED condition
    static class OptionalConfiguration {

        /**
         * REQUIRED comprehensive JavaDoc for EVERY bean method
         */
        @Bean
        @ConditionalOnMissingBean  // MANDATORY for all service beans
        public OptionalFeature optionalFeature() {
            // REQUIRED: Document initialization process
            return new OptionalFeatureImpl();
        }
    }

    // ==========================================
    // COMPONENT IMPORTS - Explicitly imported components
    // ==========================================

    /**
     * Configuration for component-annotated classes.
     * <p>
     * REQUIRED documentation for the component group purpose
     * </p>
     */
    @Configuration(proxyBeanMethods = false) // RECOMMENDED: false for performance if no inter-bean calls are proxied
    @Import({
        // REQUIRED: Organized by layer/purpose with clear comments
        // REPOSITORY LAYER
        UserRepository.class,  // MANDATORY: Document each component's role

        // SERVICE LAYER
        UserService.class      // MANDATORY: Document each component's role
    })
    static class ComponentConfiguration {
        // Additional related @Bean definitions if needed
        // REQUIRED: Document relationship to imported components
    }

    // ==========================================
    // CUSTOMIZERS - Extension points
    // ==========================================

    /**
     * Exposes customizers for extension points.
     */
    @Configuration(proxyBeanMethods = false) // RECOMMENDED: false for performance if no inter-bean calls are proxied
    static class CustomizerConfiguration {

        /**
         * REQUIRED comprehensive documentation for customizer
         *
         * @return Customizer bean
         */
        @Bean
        @ConditionalOnMissingBean
        public MyModuleCustomizer moduleCustomizer() {
            return new DefaultMyModuleCustomizer();
        }
    }
}
```

#### MANDATORY REGISTRATION

EVERY auto-configuration class MUST be registered in:

```text
src/main/resources/META-INF/spring/org.springframework.boot.autoconfigure.AutoConfiguration.imports
```

Example content:

```text
com.tui.destilink.framework.mymodule.config.MyModuleAutoConfiguration
```

#### CODE ORGANIZATION REQUIREMENTS

1. PACKAGE STRUCTURE:
   * REQUIRED: Auto-configuration classes MUST be in `.config` package
   * REQUIRED: Properties classes MUST be in `.config` package
   * REQUIRED: Implementation classes MUST be in appropriate packages (`.service`, `.repository`, etc.)
   * VIOLATION: Mixing auto-configuration with component implementations

2. DOCUMENTATION:
   * MANDATORY: Comprehensive JavaDoc for auto-configuration class
   * MANDATORY: Detailed JavaDoc for EVERY bean method
   * MANDATORY: Comments explaining conditional activation logic
   * MANDATORY: Documentation of all customization points

3. BEAN ORGANIZATION:
   * REQUIRED: Group related beans in nested static configuration classes
   * REQUIRED: Clear separation of core beans, optional features, and customizers
   * REQUIRED: Document relationships between beans
   * VIOLATION: Flat structure with unrelated beans together
```

## Dependency Management Rules

```yaml
# METADATA
# framework: Destilink Framework
# ...existing code...
# DEPENDENCY DECLARATION PRINCIPLES
dependency_principles:
  - minimize_redundancy: |
      MANDATORY: A thorough and systematic analysis MUST be performed to confirm that each non-Destilink Framework dependency is not already included transitively.
      Process:
      1. For EVERY non-destilink dependency being added, perform a full transitive analysis
      2. Use 'mvn dependency:tree' to list ALL transitive dependencies
      3. Systematically verify each candidate dependency against the tree output
      4. Only include dependencies that are CONFIRMED not to be available transitively
      5. Document any borderline cases with explicit reasoning
      6. Re-validate ALL dependencies after any POM changes
  - prefer_framework: Use framework modules instead of direct Spring dependencies when available
  - inherit_versions: Omit version tags for dependencies already defined in parent POMs
  - explicit_scopes: Always explicitly set scope for non-compile dependencies
  - avoid_duplication: Don't repeat dependency declarations across similar modules

# DEPENDENCY SCOPES
scope_usage:
  - compile (default): Regular runtime dependencies
    - used_for: Direct compile and runtime dependencies
    - transitivity: Fully transitive to dependent projects

  - provided: Dependencies available at runtime from container/JDK
    - used_for: Dependencies present in the runtime container
    - transitivity: NOT transitive to dependent projects
    - examples: Servlet API, JavaEE/JakartaEE APIs

  - runtime: Runtime-only dependencies (not needed at compile time)
    - used_for: JDBC drivers, runtime-only libraries
    - transitivity: Transitive at runtime only

  - test: Test-only dependencies
    - used_for: Testing frameworks, test utilities
    - transitivity: NOT transitive to dependent projects
    - examples: JUnit, Mockito, framework test-support modules

  - import: Used in dependencyManagement only
    - used_for: Importing BOMs (Bills of Materials)
    - transitivity: NOT direct dependency, only version management
    - examples: Spring Boot BOM, Resilience4j BOM

# DEPENDENCY DECLARATION PATTERNS
module_patterns:
  # Framework Core Modules
  - core_modules:
      pattern: Direct dependencies with minimal transitivity
      scope: compile
      example: "<dependency><groupId>com.tui.destilink.framework</groupId><artifactId>core</artifactId></dependency>"

  # Functional Modules
  - functional_modules:
      pattern: Include only what is needed, avoid over-dependency
      scope: compile
      transitivity: Keep minimal to avoid bloat
      example: "<dependency><groupId>com.tui.destilink.framework</groupId><artifactId>redis-core</artifactId></dependency>"

  # Optional Dependencies
  - optional_modules:
      pattern: Mark as provided to prevent unwanted transitivity
      scope: provided
      example: "<dependency><groupId>com.tui.destilink.framework</groupId><artifactId>caching</artifactId><scope>provided</scope></dependency>"

  # Test Support Modules
  - test_support:
      pattern: Regular dependencies in test modules, test scope elsewhere
      scope_in_test_modules: compile
      scope_in_other_modules: test
      example: "<dependency><groupId>com.tui.destilink.framework.test-support</groupId><artifactId>redis-test-support</artifactId><scope>test</scope></dependency>"

# APPLICATION TYPE DEPENDENCIES
application_types:
  # Microservices
  - microservice:
      base_parent: framework-build-parent-ms
      required_dependencies:
        - ms-core
      common_inclusions:
        - web-core
        - web-server
        - web-security-oauth2-server
        - web-openapi

  # Cronjobs
  - cronjob:
      base_parent: framework-build-parent-cronjob
      required_dependencies:
        - cronjob-core
      common_inclusions:
        - async-core

  # Framework Modules
  - framework_modules:
      base_parent: framework-modules (parent POM)
      dependency_inclusion: Only direct dependencies
      transitive_approach: Minimize with explicit dependencies

# BOM USAGE
bom_usage:
  - client_applications:
      approach: Import framework-bom in dependencyManagement
      example: |
        <dependencyManagement>
          <dependencies>
            <dependency>
              <groupId>com.tui.destilink.framework</groupId>
              <artifactId>framework-bom</artifactId>
              <version>1.0.26</version>
              <type>pom</type>
              <scope>import</scope>
            </dependency>
          </dependencies>
        </dependencyManagement>
      benefit: Consistent versions without forced dependencies

  - framework_modules:
      approach: Do not import BOM directly within modules
      pattern: Direct dependencies on other modules with version from parent
      rationale: Avoid circular BOM references

# COMMON DEPENDENCY ISSUES
common_issues:
  - excluded_dependencies:
      problem: Excluding transitive dependencies can cause unpredictable behavior
      solution: Prefer more specific dependencies or configure conditionals properly

  - circular_dependencies:
      problem: Modules that depend on each other
      solution: Extract common functionality to a shared module

  - dependency_conflicts:
      problem: Multiple versions of the same dependency
      solution: Use dependencyManagement to enforce single version

  - redundant_dependencies:
      problem: Including dependencies already available through transitive dependencies
      solution: Only declare direct dependencies, rely on transitive dependencies where appropriate
      example: "Don't include spring-boot-starter-web when ms-core is already included"

# DEPENDENCY EXAMPLES
correct_examples:
  # External dependency used by only one module
  - module_specific_external:
      description: "External dependency used by only this module"
      example: |
        <!-- In framework-modules/workflow/workflow-iwf/pom.xml -->
        <properties>
            <iwf.version>2.5.0</iwf.version>
        </properties>
        <dependencies>
            <dependency>
                <groupId>io.iworkflow</groupId>
                <artifactId>iwf-java-sdk</artifactId>
                <version>${iwf.version}</version>
            </dependency>
        </dependencies>

  # Standard framework module dependency
  - framework_module_dependency:
      description: "Framework module depending on another framework module"
      example: |
        <!-- No version specified - inherited from parent -->
        <dependency>
            <groupId>com.tui.destilink.framework</groupId>
            <artifactId>ms-core</artifactId>
        </dependency>

  # BOM-managed external dependency
  - bom_managed_dependency:
      description: "Dependency managed by imported BOM"
      example: |
        <!-- In framework-dependencies-parent/pom.xml -->
        <properties>
            <resilience4j.version>2.3.0</resilience4j.version>
        </properties>
        <dependencyManagement>
            <dependencies>
                <dependency>
                    <groupId>io.github.resilience4j</groupId>
                    <artifactId>resilience4j-bom</artifactId>
                    <version>${resilience4j.version}</version>
                    <type>pom</type>
                    <scope>import</scope>
                </dependency>
            </dependencies>
        </dependencyManagement>

        <!-- In a module -->
        <dependency>
            <groupId>io.github.resilience4j</groupId>
            <artifactId>resilience4j-retry</artifactId>
            <!-- No version - comes from BOM -->
        </dependency>

# TEST DEPENDENCIES
test_dependencies:
  - test_support_modules:
      purpose: Streamline testing with reusable components
      usage: |
        MANDATORY: ALL tests (unit and integration) MUST use appropriate test-support modules.
        The following rules MUST be enforced without exception:
        1. ALL test classes MUST include at minimum the test-core module
        2. NEVER use standard Spring Boot test dependencies directly; ALWAYS use test-support modules
        3. For each technology being tested, the corresponding test-support module MUST be used
        4. Unit tests and integration tests alike MUST use test-support modules
        5. If a specific test-support module doesn't exist, developers MUST create one
        6. No exceptions or workarounds to these rules are permitted
      example: |
        <!-- REQUIRED for ALL tests -->
        <dependency>
          <groupId>com.tui.destilink.framework.test-support</groupId>
          <artifactId>test-core</artifactId>
          <scope>test</scope>
        </dependency>

        <!-- Technology-specific test support -->
        <dependency>
          <groupId>com.tui.destilink.framework.test-support</groupId>
          <artifactId>redis-test-support</artifactId>
          <scope>test</scope>
        </dependency>

  - framework_test_applications:
      purpose: Integration testing across multiple modules
      pattern: Direct dependency on modules being tested
      scope: compile (regular dependency)
      requirement: |
        MANDATORY: Even in test applications, test-support modules MUST be used instead of direct Spring test dependencies
```

## Logging Framework

```yaml
# CORE LOGGING RULES
logger: org.slf4j.Logger ONLY
format: JSON structured logging via Logback and Logstash Encoder
prohibited: Direct use of Logback classes in application code

# MDC CONTEXT
base_class: AbstractContextDecorator<T>
usage: try-with-resources with createClosableScope()
examples:
  - TripsContextDecorator: TRIPS fields
  - LockContextDecorator: Locking context
key_prefixes:
  - trips.*: TRIPS-related context
  - lockcontext.*: Locking-related context

# LOG MARKERS
base_class: net.logstash.logback.marker.Markers
specialized_markers:
  - MonitoringMarkerUtils: Datadog monitoring markers
  - ConstraintViolationMarker: Jakarta Validation violations
  - SqsMessageMarker: SQS message details
  - SnsPublishMarker: SNS publish details
  - CloudEventsMarker: CloudEvent details

# EXCEPTION HANDLING IN LOGS
exception_classes:
  - MarkerNestedRuntimeException: Runtime exceptions with monitoring markers
  - MarkerNestedException: Checked exceptions with monitoring markers
  - LogMutingException: Exceptions with suppressed logging

# MESSAGE SIZE LIMITING
utility: LoggingUtils.limitToBytes()
implementation: LongMessagesConverter in logback-spring.xml

# HTTP LOGGING
direction_model: HttpDirection enum (SERVER/CLIENT)
log_levels:
  - 5xx responses: ERROR level
  - 4xx responses: WARN level
  - Other responses: INFO level
```

## Error Handling Patterns

```yaml
# WEB ERROR HANDLING
primary_mechanism: Spring @ControllerAdvice with ProblemDetail (RFC 7807)
handlers:
  - ProblemDetailsExceptionHandler: Common exceptions
  - SecurityProblemDetailsExceptionHandler: Auth/access exceptions (401/403)
custom_handlers: Create @ControllerAdvice with @ExceptionHandler returning ResponseEntity<ProblemDetail>

# EXCEPTION HIERARCHY
base_exceptions:
  - MarkerNestedRuntimeException: For runtime exceptions with monitoring markers
  - MarkerNestedException: For checked exceptions with monitoring markers

# ERROR MODELS
pattern: Value objects for error representation
annotations: @Value, @SuperBuilder
naming_convention: Error + descriptor (e.g., ErrorCause)
```

## Module Dependency Graph

```text
core <-- jackson-core
  ^
  |
  +-- web-core <-- web-client, web-server, web-security-oauth2-server, web-openapi
  |
  +-- async-core
  |
  +-- cloudevents
  |
  +-- redis-core <-- locking-redis-lock, locking-shedlock, aws-elasticache-redis
  |
  +-- resilience-retry, resilience-circuitbreaker
  |
  +-- aws-core <-- aws-sns, aws-sqs, aws-s3, aws-elasticache-redis, aws-opensearch, aws-msk-auth-iam, aws-aurora-postgresql
  |
  +-- file-storage <-- file-download
  |
  +-- workflow <-- workflow-iwf
```

## Testing Framework

```yaml
# TESTING ENVIRONMENT
primary_environment: utils/ Docker Compose setup (Redis, PostgreSQL, Keycloak, LocalStack)
prohibited: Direct use of Testcontainers in CI/CD pipeline

# TEST TYPES
unit_tests:
  - Framework: JUnit 5, Mockito, AssertJ
  - Naming: *Test suffix
  - Location: src/test/java, mirroring main package structure
  - Focus: Individual units in isolation, mocking dependencies

integration_tests:
  - Framework: @SpringBootTest
  - Naming: *IT suffix
  - Location: src/test/java, mirroring main package structure
  - External dependencies: Use test-support modules

framework_test_applications:
  - Purpose: Test interactions between multiple modules
  - Location: framework-test-applications/
  - External services: utils/ Docker Compose setup
  - Naming: *IT suffix

# TEST SUPPORT MODULES NAMING CONVENTIONS
test_support_module_naming:
  - Module naming: technology-test-support (e.g., redis-test-support, kafka-test-support)
  - Package naming: com.tui.destilink.framework.test.support.<technology>
  - Annotation naming: @TechnologyTestSupport (e.g., @RedisTestSupport, @KeycloakTestSupport)
  - Configuration files: 1000-<technology>-test-support.application.test.yml
  - Auto-configuration: TechnologyTestSupportAutoConfiguration
  - Test execution listeners: TechnologyTestExecutionListener

# TEST SUPPORT MODULES
test_core:
  - @TestClassId: Unique ID for test resources
  - TestUtils.generateTestClassId(): Programmatic ID generation
  - TestUtils.generateReproducibleUUID(): Deterministic UUIDs

redis_test_support:
  - @RedisTestSupport: Configure Spring context for Redis

s_ftp_test_support:
  - @FakeSFTPSupport: SFTP testing without real server
  - FakeSFTPServer: Mock SFTP server

keycloak_test_support:
  - @KeycloakTestSupport: Set up Keycloak realm, clients, users
  - KeycloakAdminService: Interact with test realm

kafka_test_support:
  - @EmbeddedKafka: Spring Kafka support

postgresql_test_support:
  - @PostgresTestSupport: Provision dedicated database per test class
```

## Code Style Rules

```yaml
# LOMBOK USAGE
annotations:
  - @Data: For DTOs and value objects
  - @Builder: For builder pattern
  - @RequiredArgsConstructor: For dependency injection
  - @Slf4j: For logging
  - @Value: For immutable value objects
  - @UtilityClass: For utility classes

# DEPENDENCY INJECTION
preferred: Constructor injection with final fields
prohibited: @Autowired field injection

# FIELD MODIFIERS
rule: Use final for all fields that are not reassigned

# IMPORTS
prohibited: Wildcard imports

# API DESIGN
preferred: Fluent APIs where appropriate (builders)
interface_design: Base interface + specialized variant (e.g., StorageService → VersionedStorageService)
implementation_hiding: Interface in root package, implementation in .impl subpackage

# CONSTANTS
organization: Static final fields at class top
naming: UPPER_CASE_WITH_UNDERSCORES
grouping: Group related constants together

# CONCURRENCY STYLE
preferred: Virtual threads for IO-bound operations (Java 21+)
implementation: @Bean ExecutorService virtualThreadExecutor() { return Executors.newVirtualThreadPerTaskExecutor(); }
usage: CompletableFuture.supplyAsync(supplier, virtualThreadExecutor)
prohibited: Manually creating large thread pools for IO-bound operations
```

## Decision Trees

```yaml
# WHEN TO CREATE NEW MODULE
if new_functional_area:
  create_top_level_module
elif extending_existing_area:
  if independent_functionality:
    create_submodule
  else:
    extend_existing_module

# VERSIONING DECISIONS
if backward_incompatible:
  increment_major
elif new_feature:
  increment_minor
elif bugfix_or_dependency_update:
  increment_patch

# EXTERNAL DEPENDENCY HANDLING
if test_requires_external_dependency:
  if test_support_module_exists:
    use_test_support_module
  elif simple_dependency_or_isolation_not_critical:
    mock_dependency
  else:
    create_new_test_support_module
  never:
    use_testcontainers_directly_in_ci
```

## Anti-Patterns

```yaml
# CRITICAL ANTI-PATTERNS - STRICTLY PROHIBITED

## Dependency Management Violations
- TRANSITIVE DEPENDENCY VERIFICATION
  * MANDATORY: The LLM MUST iterate over EVERY SINGLE dependency that is not part of the destilink-framework
  * MANDATORY: For EACH dependency, verify with a confidence of 9.5 out of 10 that it is NOT included transitively
  * MANDATORY STEPS:
    1. Use 'mvn dependency:tree -Dverbose' to generate complete transitive dependency tree
    2. For EACH new dependency, systematically search the tree output
    3. Create a verification table listing each dependency and its transitive status
    4. ONLY include dependencies confirmed NOT to be available transitively
    5. Document this verification process in code comments
  * CRITICAL VIOLATIONS:
    - Adding ANY Spring Boot starter that comes with ms-core/cronjob-core
    - Adding ANY dependency without documented transitive analysis
    - Adding ANY dependency that appears in the mvn dependency:tree output
    - Ignoring transitive dependency verification for ANY dependency

## Testing Framework Violations
- MANDATORY TEST-SUPPORT MODULE USAGE
  * ABSOLUTE REQUIREMENT: ALL tests (unit AND integration) MUST use test-support modules
  * MANDATORY INCLUSIONS:
    1. EVERY test class MUST include the test-core module - NO EXCEPTIONS
    2. ANY test using Spring functionality MUST use appropriate test-support module
    3. Technology-specific tests MUST use corresponding test-support module
  * PROHIBITED PRACTICES:
    - NEVER use standard Spring Boot test dependencies directly - NO EXCEPTIONS
    - NEVER create test classes without test-support module dependencies
    - NEVER use @SpringBootTest without appropriate test-support modules
    - NEVER use standard testing libraries when test-support equivalents exist
  * TEST MODULE CREATION RULE:
    - If a specific test-support module doesn't exist, developers MUST create one
    - NO ad-hoc testing approaches allowed outside the test-support framework

## Component Scanning Violations
- ABSOLUTE PROHIBITION ON COMPONENT SCANNING
  * FORBIDDEN: ANY form of component scanning in modules
  * FORBIDDEN: @ComponentScan annotation in ANY configuration class
  * FORBIDDEN: @SpringBootApplication with default scanning behavior
  * FORBIDDEN: Relying on classpath scanning for ANY component discovery
  * CONSEQUENCES: Immediate rejection of any code using component scanning

## Dependency Injection Violations
- FIELD INJECTION STRICTLY PROHIBITED
  * FORBIDDEN: @Autowired on fields
  * FORBIDDEN: Mixed injection styles in same class
  * FORBIDDEN: @Value annotations on fields
  * REQUIRED: Constructor injection ONLY for ALL dependencies

## Configuration Violations
- MODULE CONFIGURATION ISOLATION
  * FORBIDDEN: Modifying top-level POMs directly
  * FORBIDDEN: Mixing module-specific and framework configuration
  * FORBIDDEN: Beans without appropriate conditional annotations
  * FORBIDDEN: Direct bean references between configuration classes
  * MANDATORY: Strict separation of configuration concerns

## General Anti-Patterns
- STRICTLY PROHIBITED PRACTICES:
  * Use Logback classes directly
  * Use wildcard imports
  * Use Testcontainers directly in CI tests
  * Add dependencies directly to module pom.xml unless module-specific
  * Create complex domain models
  * Register beans without appropriate conditional annotations
  * Rely on auto-wiring for components that should be explicitly imported
  * Define framework-wide dependency versions in individual module POMs
  * Use conflicting Spring Boot versions within the same application
  * Add explicit version tags for dependencies already versioned by parent POMs
```

## Version Handling

```yaml
# VERSIONING SCHEME
format: major.minor.patch-SNAPSHOT
major: Backward-incompatible API changes
minor: New features or significant non-breaking changes
patch: Bug fixes, minor updates, dependency updates
snapshot: Ongoing development build

# RELEASE PROCESS
trigger: Git tag v<major>_<minor>_<patch>
ci_pipeline: Automatic build and publish based on tag
next_version: X.Y.(Z+1)-SNAPSHOT after release
```