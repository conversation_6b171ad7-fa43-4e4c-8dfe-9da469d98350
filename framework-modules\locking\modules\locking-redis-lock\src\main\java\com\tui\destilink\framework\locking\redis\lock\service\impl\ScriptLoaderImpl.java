package com.tui.destilink.framework.locking.redis.lock.service.impl;

import com.tui.destilink.framework.locking.redis.lock.exception.LockInternalException;
import com.tui.destilink.framework.locking.redis.lock.service.ScriptLoader;
import com.tui.destilink.framework.redis.core.script.ImmutableLettuceScript;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.script.RedisScript;

/**
 * Implementation of the {@link ScriptLoader} interface that loads and caches
 * Redis Lua scripts
 * used by the locking mechanism.
 * <p>
 * This implementation uses {@link ImmutableLettuceScript} to load scripts from
 * the classpath
 * and caches them for subsequent use. Scripts are loaded from the 'lua'
 * directory in the classpath.
 * </p>
 * <p>
 * The scripts are loaded once during initialization and cached for the lifetime
 * of the application.
 * This improves performance by avoiding repeated loading and parsing of
 * scripts.
 * </p>
 */
@Slf4j
public class ScriptLoaderImpl implements ScriptLoader {

    /**
     * Base path for Lua scripts in the classpath.
     */
    private static final String SCRIPT_BASE_PATH = "lua/";

    /**
     * Script for acquiring a lock.
     */
    private final RedisScript<Long> acquireLockScript;

    /**
     * Script for releasing a lock.
     */
    private final RedisScript<Long> releaseLockScript;

    /**
     * Script for extending a lock's TTL.
     */
    private final RedisScript<Long> extendLockScript;

    /**
     * Script for checking if a lock exists.
     */
    private final RedisScript<Long> checkLockScript;

    /**
     * Script for trying to acquire a lock.
     */
    private final RedisScript<Long> tryLockScript;

    /**
     * Script for updating a state lock's state.
     */
    private final RedisScript<Long> updateStateScript;

    /**
     * Script for updating a state lock's state if it matches the expected state.
     */
    private final RedisScript<Long> updateStateIfEqualsScript;

    /**
     * Script for trying to acquire a state lock.
     */
    private final RedisScript<Long> tryStateLockScript;

    /**
     * Script for releasing a state lock.
     */
    private final RedisScript<Long> unlockStateLockScript;

    /**
     * Script for refreshing a lock's lease time by the watchdog.
     */
    private final RedisScript<Long> watchdogRefreshLockScript;

    /**
     * Creates a new ScriptLoaderImpl and loads all required scripts from the
     * classpath.
     * <p>
     * Scripts are loaded during initialization to fail fast if any script is
     * missing or invalid.
     * </p>
     */
    public ScriptLoaderImpl() {
        log.debug("Initializing ScriptLoader and loading Redis Lua scripts");
        try {
            this.acquireLockScript = loadScript("lock.lua", Long.class);
            this.releaseLockScript = loadScript("unlock.lua", Long.class);
            this.extendLockScript = loadScript("renew.lua", Long.class);
            this.checkLockScript = loadScript("check-lock.lua", Long.class);
            this.tryLockScript = loadScript("try_lock.lua", Long.class);
            this.updateStateScript = loadScript("update_state.lua", Long.class);
            this.updateStateIfEqualsScript = loadScript("update_state_if_equals.lua", Long.class);
            this.tryStateLockScript = loadScript("try_state_lock.lua", Long.class);
            this.unlockStateLockScript = loadScript("unlock_state_lock.lua", Long.class);
            this.watchdogRefreshLockScript = loadScript("watchdog_refresh_lock.lua", Long.class);
            log.debug("Successfully loaded all Redis Lua scripts for locking");
        } catch (Exception e) {
            log.error("Failed to load Redis Lua scripts for locking", e);
            throw new LockInternalException(
                    null, // lockName - not applicable during initialization
                    "ScriptLoader", // lockType - using component name as type
                    null, // lockOwnerId - not applicable during initialization
                    null, // requestUuid - not applicable during initialization
                    "ScriptLoader", // component
                    "initialization", // operation
                    "Failed to load Redis Lua scripts", // message
                    e // cause
            );
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String loadScript(String scriptPath) {
        log.debug("Loading Redis Lua script from path: {}", scriptPath);
        try {
            // Extract the script name from the path
            String scriptName = scriptPath;
            if (scriptPath.startsWith("classpath:")) {
                scriptName = scriptPath.substring(scriptPath.lastIndexOf('/') + 1);
            }

            // Load the script and return its SHA
            RedisScript<Long> script = loadScript(scriptName, Long.class);
            return script.getSha1();
        } catch (Exception e) {
            log.error("Failed to load Redis Lua script from path: {}", scriptPath, e);
            throw new LockInternalException(
                    null, // lockName - not applicable during initialization
                    "ScriptLoader", // lockType - using component name as type
                    null, // lockOwnerId - not applicable during initialization
                    null, // requestUuid - not applicable during initialization
                    "ScriptLoader", // component
                    "loadScript", // operation
                    "Failed to load Redis Lua script from path: " + scriptPath, // message
                    e // cause
            );
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public RedisScript<Long> getAcquireLockScript() {
        return acquireLockScript;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public RedisScript<Long> getReleaseLockScript() {
        return releaseLockScript;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public RedisScript<Long> getExtendLockScript() {
        return extendLockScript;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public RedisScript<Long> getCheckLockScript() {
        return checkLockScript;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public RedisScript<Long> getTryLockScript() {
        return tryLockScript;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public RedisScript<Long> getUpdateStateScript() {
        return updateStateScript;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public RedisScript<Long> getUpdateStateIfEqualsScript() {
        return updateStateIfEqualsScript;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public RedisScript<Long> getTryStateLockScript() {
        return tryStateLockScript;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public RedisScript<Long> getUnlockStateLockScript() {
        return unlockStateLockScript;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public RedisScript<Long> getWatchdogRefreshLockScript() {
        return watchdogRefreshLockScript;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <T> RedisScript<T> loadScript(String scriptPath, Class<T> resultType) {
        log.debug("Loading Redis Lua script with result type: {}", scriptPath);
        try {
            // Extract the script name from the path
            String scriptName = scriptPath;
            if (scriptPath.startsWith("classpath:")) {
                scriptName = scriptPath.substring(scriptPath.lastIndexOf('/') + 1);
            } else if (scriptPath.contains("/")) {
                scriptName = scriptPath.substring(scriptPath.lastIndexOf('/') + 1);
            }

            // Load the script and return it
            return loadScriptInternal(scriptName, resultType);
        } catch (Exception e) {
            log.error("Failed to load Redis Lua script from path: {}", scriptPath, e);
            throw new LockInternalException(
                    null, // lockName - not applicable during initialization
                    "ScriptLoader", // lockType - using component name as type
                    null, // lockOwnerId - not applicable during initialization
                    null, // requestUuid - not applicable during initialization
                    "ScriptLoader", // component
                    "loadScript", // operation
                    "Failed to load Redis Lua script from path: " + scriptPath, // message
                    e // cause
            );
        }
    }

    /**
     * Loads a script from the classpath.
     *
     * @param scriptName The name of the script file
     * @param resultType The expected result type of the script
     * @param <T>        The type parameter for the result type
     * @return The loaded script
     */
    private <T> RedisScript<T> loadScriptInternal(String scriptName, Class<T> resultType) {
        String scriptPath = SCRIPT_BASE_PATH + scriptName;
        log.debug("Loading Redis Lua script: {}", scriptPath);
        return ImmutableLettuceScript.loadFromClasspath(scriptPath, resultType);
    }
}