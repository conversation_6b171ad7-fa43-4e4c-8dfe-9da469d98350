package com.tui.destilink.framework.locking.redis.lock.test;

import com.tui.destilink.framework.locking.redis.lock.config.RedisLockAutoConfiguration;
import com.tui.destilink.framework.redis.core.RedisCoreAutoConfiguration;
import com.tui.destilink.framework.redis.core.cluster.ClusterCommandExecutor;
import com.tui.destilink.framework.redis.core.config.RedisCoreProperties;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.data.redis.connection.RedisConnectionFactory;

/**
 * Test application for the locking-redis-lock module.
 * It is used by all Spring Boot tests in this module.
 * It enables auto-configuration and imports the test-specific Redis lock configuration.
 * <p>
 * Note: RedisCoreAutoConfiguration is excluded because it requires Redis cluster mode,
 * but tests use standalone Redis via redis-test-support.
 */
@Configuration(proxyBeanMethods = false)
@EnableAutoConfiguration(exclude = RedisCoreAutoConfiguration.class)
@Import({TestRedisLockConfiguration.class, RedisLockAutoConfiguration.class})
public class TestApplication {

    /**
     * Provides RedisCoreProperties for tests since we exclude RedisCoreAutoConfiguration.
     * This allows test-support modules to work properly.
     */
    @Bean
    public RedisCoreProperties redisCoreProperties() {
        RedisCoreProperties properties = new RedisCoreProperties();
        return properties;
    }

    /**
     * Provides ClusterCommandExecutor for tests using a mock implementation.
     * This allows Redis lock operations to work with standalone Redis in tests.
     */
    @Bean
    public ClusterCommandExecutor clusterCommandExecutor(RedisConnectionFactory redisConnectionFactory) {
        return new MockClusterCommandExecutor(redisConnectionFactory);
    }
}