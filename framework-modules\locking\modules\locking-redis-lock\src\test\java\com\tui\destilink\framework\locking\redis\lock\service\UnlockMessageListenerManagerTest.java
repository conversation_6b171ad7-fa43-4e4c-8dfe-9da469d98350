package com.tui.destilink.framework.locking.redis.lock.service;

import com.tui.destilink.framework.locking.redis.lock.model.UnlockType;
import com.tui.destilink.framework.locking.redis.lock.test.TestApplication;
import com.tui.destilink.framework.test.support.core.util.TestUtils;
import com.tui.destilink.framework.test.support.redis.annotation.RedisTestSupport;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.connection.MessageListener;
import org.springframework.data.redis.listener.ChannelTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.data.redis.listener.Topic;
import org.springframework.test.annotation.DirtiesContext;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatCode;

/**
 * Test suite for {@link UnlockMessageListenerManager}.
 * <p>
 * Tests ensure that:
 * - Listeners are created and registered correctly per bucket
 * - Channel patterns are built correctly
 * - Semaphore holders are managed properly
 * - Cleanup works correctly during shutdown
 * - Thread safety for concurrent access
 * </p>
 */
@RedisTestSupport(keyspacePrefixes = { "test-unlock:" })
@SpringBootTest(classes = TestApplication.class, webEnvironment = SpringBootTest.WebEnvironment.NONE)
@DirtiesContext(classMode = DirtiesContext.ClassMode.BEFORE_EACH_TEST_METHOD)
class UnlockMessageListenerManagerTest {

    private static final String UNIQUE_ID = TestUtils.generateTestClassId(UnlockMessageListenerManagerTest.class);
    private static final String BUCKET_NAME = "test-bucket-" + UNIQUE_ID;
    // Use proper hash tag for Redis cluster compatibility and correct prefix for authentication
    private static final String KEY_PREFIX = UNIQUE_ID + ":test-unlock:";
    private static final String LOCK_NAME = "test-lock-" + UNIQUE_ID;

    @Autowired
    private RedisMessageListenerContainer listenerContainer;
    
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    
    private Executor unlockMessageExecutor;
    private UnlockMessageListenerManager manager;

    @BeforeEach
    void setUp() {
        // Use a direct executor for synchronous testing
        unlockMessageExecutor = Runnable::run;
        manager = new UnlockMessageListenerManager(listenerContainer, unlockMessageExecutor);
    }

    @Test
    @DisplayName("Should create and register listener for new bucket")
    void shouldCreateAndRegisterListenerForNewBucket() throws InterruptedException {
        // Execute
        UnlockMessageListener listener = manager.getOrCreateListenerForBucket(BUCKET_NAME, KEY_PREFIX);

        // Verify
        assertThat(listener).isNotNull();
        assertThat(listener.getBucketName()).isEqualTo(BUCKET_NAME);
        assertThat(listener.getChannelPattern()).isEqualTo(KEY_PREFIX + BUCKET_NAME + ":__unlock_channels__:*");
        
        // Verify listener is working by sending a message and checking if it's received
        CountDownLatch latch = new CountDownLatch(1);
        
        // Create a semaphore holder that counts down the latch when signaled
        LockSemaphoreHolder semaphoreHolder = new LockSemaphoreHolder() {
            @Override
            public void signal() {
                super.signal();
                latch.countDown();
            }
            
            @Override
            public int getWaitersCount() {
                return 1;
            }
        };
        
        // Register the semaphore holder with the listener
        listener.registerLockSemaphoreHolder(LOCK_NAME, semaphoreHolder);
        
        // Send a message to the channel
        String channel = KEY_PREFIX + BUCKET_NAME + ":__unlock_channels__:{" + UNIQUE_ID + "}:" + LOCK_NAME;
        redisTemplate.convertAndSend(channel, UnlockType.REENTRANT_FULLY_RELEASED.name());
        
        // Wait for the latch to be counted down
        assertThat(latch.await(5, TimeUnit.SECONDS)).isTrue();
    }

    @Test
    @DisplayName("Should return existing listener for same bucket")
    void shouldReturnExistingListenerForSameBucket() throws InterruptedException {
        // Execute - create listener first time
        UnlockMessageListener firstCall = manager.getOrCreateListenerForBucket(BUCKET_NAME, KEY_PREFIX);
        
        // Execute - get listener second time
        UnlockMessageListener secondCall = manager.getOrCreateListenerForBucket(BUCKET_NAME, KEY_PREFIX);

        // Verify
        assertThat(firstCall).isSameAs(secondCall);
        
        // Verify both references work by sending a message and checking if it's received
        CountDownLatch latch = new CountDownLatch(1);
        
        // Create a semaphore holder that counts down the latch when signaled
        LockSemaphoreHolder semaphoreHolder = new LockSemaphoreHolder() {
            @Override
            public void signal() {
                super.signal();
                latch.countDown();
            }
            
            @Override
            public int getWaitersCount() {
                return 1;
            }
        };
        
        // Register the semaphore holder with the second reference
        secondCall.registerLockSemaphoreHolder(LOCK_NAME, semaphoreHolder);
        
        // Send a message to the channel
        String channel = KEY_PREFIX + BUCKET_NAME + ":__unlock_channels__:{" + UNIQUE_ID + "}:" + LOCK_NAME;
        redisTemplate.convertAndSend(channel, UnlockType.REENTRANT_FULLY_RELEASED.name());
        
        // Wait for the latch to be counted down
        assertThat(latch.await(5, TimeUnit.SECONDS)).isTrue();
    }

    @Test
    @DisplayName("Should create different listeners for different buckets")
    void shouldCreateDifferentListenersForDifferentBuckets() throws InterruptedException {
        String bucket1 = "bucket-1-" + UNIQUE_ID;
        String bucket2 = "bucket-2-" + UNIQUE_ID;
        
        // Execute
        UnlockMessageListener listener1 = manager.getOrCreateListenerForBucket(bucket1, KEY_PREFIX);
        UnlockMessageListener listener2 = manager.getOrCreateListenerForBucket(bucket2, KEY_PREFIX);

        // Verify
        assertThat(listener1).isNotSameAs(listener2);
        assertThat(listener1.getBucketName()).isEqualTo(bucket1);
        assertThat(listener2.getBucketName()).isEqualTo(bucket2);
        
        // Verify both listeners work independently by sending messages to each
        CountDownLatch latch1 = new CountDownLatch(1);
        CountDownLatch latch2 = new CountDownLatch(1);
        
        // Create semaphore holders that count down the latches when signaled
        LockSemaphoreHolder semaphoreHolder1 = new LockSemaphoreHolder() {
            @Override
            public void signal() {
                super.signal();
                latch1.countDown();
            }
            
            @Override
            public int getWaitersCount() {
                return 1;
            }
        };
        
        LockSemaphoreHolder semaphoreHolder2 = new LockSemaphoreHolder() {
            @Override
            public void signal() {
                super.signal();
                latch2.countDown();
            }
            
            @Override
            public int getWaitersCount() {
                return 1;
            }
        };
        
        // Register the semaphore holders with their respective listeners
        listener1.registerLockSemaphoreHolder(LOCK_NAME, semaphoreHolder1);
        listener2.registerLockSemaphoreHolder(LOCK_NAME, semaphoreHolder2);
        
        // Send messages to each channel
        String channel1 = KEY_PREFIX + bucket1 + ":__unlock_channels__:{" + UNIQUE_ID + "}:" + LOCK_NAME;
        String channel2 = KEY_PREFIX + bucket2 + ":__unlock_channels__:{" + UNIQUE_ID + "}:" + LOCK_NAME;
        
        redisTemplate.convertAndSend(channel1, UnlockType.REENTRANT_FULLY_RELEASED.name());
        redisTemplate.convertAndSend(channel2, UnlockType.REENTRANT_FULLY_RELEASED.name());
        
        // Wait for both latches to be counted down
        assertThat(latch1.await(5, TimeUnit.SECONDS)).isTrue();
        assertThat(latch2.await(5, TimeUnit.SECONDS)).isTrue();
    }

    @Test
    @DisplayName("Should build channel pattern correctly")
    void shouldBuildChannelPatternCorrectly() throws InterruptedException {
        // Test various key prefixes
        String[] keyPrefixes = {
            "simple:",
            "complex:prefix:",
            "prefix:with:multiple:segments:"
        };
        
        String[] expectedPatterns = {
            "simple:bucket-0-" + UNIQUE_ID + ":__unlock_channels__:*",
            "complex:prefix:bucket-1-" + UNIQUE_ID + ":__unlock_channels__:*",
            "prefix:with:multiple:segments:bucket-2-" + UNIQUE_ID + ":__unlock_channels__:*"
        };
        
        for (int i = 0; i < keyPrefixes.length; i++) {
            String bucketName = "bucket-" + i + "-" + UNIQUE_ID;
            UnlockMessageListener listener = manager.getOrCreateListenerForBucket(bucketName, keyPrefixes[i]);
            
            // Verify the channel pattern is built correctly
            assertThat(listener.getChannelPattern()).isEqualTo(expectedPatterns[i]);
            
            // Verify the listener works by sending a message and checking if it's received
            CountDownLatch latch = new CountDownLatch(1);
            
            // Create a semaphore holder that counts down the latch when signaled
            LockSemaphoreHolder semaphoreHolder = new LockSemaphoreHolder() {
                @Override
                public void signal() {
                    super.signal();
                    latch.countDown();
                }
                
                @Override
                public int getWaitersCount() {
                    return 1;
                }
            };
            
            // Register the semaphore holder with the listener
            listener.registerLockSemaphoreHolder(LOCK_NAME, semaphoreHolder);
            
            // Send a message to the channel
            String channel = keyPrefixes[i] + bucketName + ":__unlock_channels__:{" + UNIQUE_ID + "}:" + LOCK_NAME;
            redisTemplate.convertAndSend(channel, UnlockType.REENTRANT_FULLY_RELEASED.name());
            
            // Wait for the latch to be counted down
            assertThat(latch.await(5, TimeUnit.SECONDS)).isTrue();
        }
    }

    @Test
    @DisplayName("Should create and manage semaphore holders")
    void shouldCreateAndManageSemaphoreHolders() throws InterruptedException {
        // Execute - create semaphore holder
        LockSemaphoreHolder holder = manager.getOrCreateSemaphoreHolder(BUCKET_NAME, KEY_PREFIX, LOCK_NAME);

        // Verify
        assertThat(holder).isNotNull();
        
        // Verify the semaphore holder works by sending a message and checking if it's received
        CountDownLatch latch = new CountDownLatch(1);
        
        // Create a wrapper around the holder to count down the latch when signaled
        holder = new LockSemaphoreHolder() {
            @Override
            public void signal() {
                super.signal();
                latch.countDown();
            }
            
            @Override
            public int getWaitersCount() {
                return 1;
            }
        };
        
        // Replace the original holder with our wrapper
        manager.removeSemaphoreHolder(BUCKET_NAME, KEY_PREFIX, LOCK_NAME);
        manager.getOrCreateListenerForBucket(BUCKET_NAME, KEY_PREFIX)
               .registerLockSemaphoreHolder(LOCK_NAME, holder);
        
        // Send a message to the channel
        String channel = KEY_PREFIX + BUCKET_NAME + ":__unlock_channels__:{" + UNIQUE_ID + "}:" + LOCK_NAME;
        redisTemplate.convertAndSend(channel, UnlockType.REENTRANT_FULLY_RELEASED.name());
        
        // Wait for the latch to be counted down
        assertThat(latch.await(5, TimeUnit.SECONDS)).isTrue();
        
        // Execute - remove semaphore holder
        LockSemaphoreHolder removed = manager.removeSemaphoreHolder(BUCKET_NAME, KEY_PREFIX, LOCK_NAME);
        
        // Verify
        assertThat(removed).isSameAs(holder);
    }

    @Test
    @DisplayName("Should return null when removing non-existent semaphore holder")
    void shouldReturnNullWhenRemovingNonExistentSemaphoreHolder() {
        // Create a unique bucket name that doesn't exist
        String nonExistentBucket = "non-existent-bucket-" + UNIQUE_ID;
        
        // Execute - try to remove from non-existent bucket
        LockSemaphoreHolder removed = manager.removeSemaphoreHolder(nonExistentBucket, KEY_PREFIX, LOCK_NAME);

        // Verify
        assertThat(removed).isNull();
    }

    @Test
    @DisplayName("Should return null when removing from existing bucket but non-existent lock")
    void shouldReturnNullWhenRemovingFromExistingBucketButNonExistentLock() {
        // Setup - create listener for bucket
        manager.getOrCreateListenerForBucket(BUCKET_NAME, KEY_PREFIX);
        
        // Create a unique lock name that doesn't exist
        String nonExistentLock = "non-existent-lock-" + UNIQUE_ID;
        
        // Execute - try to remove non-existent lock
        LockSemaphoreHolder removed = manager.removeSemaphoreHolder(BUCKET_NAME, KEY_PREFIX, nonExistentLock);

        // Verify
        assertThat(removed).isNull();
    }

    @Test
    @DisplayName("Should handle concurrent access to same bucket")
    void shouldHandleConcurrentAccessToSameBucket() throws InterruptedException {
        final int threadCount = 10;
        final UnlockMessageListener[] listeners = new UnlockMessageListener[threadCount];
        final Thread[] threads = new Thread[threadCount];
        
        // Create a unique bucket name for this test
        String concurrentBucketName = "concurrent-bucket-" + UNIQUE_ID;
        
        // Create multiple threads trying to get listener for same bucket
        for (int i = 0; i < threadCount; i++) {
            final int index = i;
            threads[i] = new Thread(() -> {
                listeners[index] = manager.getOrCreateListenerForBucket(concurrentBucketName, KEY_PREFIX);
            });
        }
        
        // Start all threads
        for (Thread thread : threads) {
            thread.start();
        }
        
        // Wait for all threads to complete
        for (Thread thread : threads) {
            thread.join();
        }
        
        // Verify all threads got the same listener instance
        UnlockMessageListener firstListener = listeners[0];
        assertThat(firstListener).isNotNull();
        
        for (int i = 1; i < threadCount; i++) {
            assertThat(listeners[i]).isSameAs(firstListener);
        }
        
        // Verify the listener works by sending a message and checking if it's received
        CountDownLatch latch = new CountDownLatch(1);
        
        // Create a semaphore holder that counts down the latch when signaled
        LockSemaphoreHolder semaphoreHolder = new LockSemaphoreHolder() {
            @Override
            public void signal() {
                super.signal();
                latch.countDown();
            }
            
            @Override
            public int getWaitersCount() {
                return 1;
            }
        };
        
        // Register the semaphore holder with the listener
        firstListener.registerLockSemaphoreHolder(LOCK_NAME, semaphoreHolder);
        
        // Send a message to the channel
        String channel = KEY_PREFIX + concurrentBucketName + ":__unlock_channels__:{" + UNIQUE_ID + "}:" + LOCK_NAME;
        redisTemplate.convertAndSend(channel, UnlockType.REENTRANT_FULLY_RELEASED.name());
        
        // Wait for the latch to be counted down
        assertThat(latch.await(5, TimeUnit.SECONDS)).isTrue();
    }

    @Test
    @DisplayName("Should unregister all listeners during shutdown")
    void shouldUnregisterAllListenersDuringShutdown() throws InterruptedException {
        // Setup - create multiple listeners with unique bucket names
        String bucket1 = "bucket-1-" + UNIQUE_ID;
        String bucket2 = "bucket-2-" + UNIQUE_ID;
        String bucket3 = "bucket-3-" + UNIQUE_ID;
        
        // Create a unique lock name for this test
        String shutdownLockName = "shutdown-lock-" + UNIQUE_ID;
        
        // Create listeners and verify they work
        UnlockMessageListener listener1 = manager.getOrCreateListenerForBucket(bucket1, KEY_PREFIX);
        UnlockMessageListener listener2 = manager.getOrCreateListenerForBucket(bucket2, KEY_PREFIX);
        UnlockMessageListener listener3 = manager.getOrCreateListenerForBucket(bucket3, KEY_PREFIX);
        
        // Create semaphore holders for each listener
        CountDownLatch latch1 = new CountDownLatch(1);
        CountDownLatch latch2 = new CountDownLatch(1);
        CountDownLatch latch3 = new CountDownLatch(1);
        
        // Register semaphore holders with each listener
        listener1.registerLockSemaphoreHolder(shutdownLockName, createCountingHolder(latch1));
        listener2.registerLockSemaphoreHolder(shutdownLockName, createCountingHolder(latch2));
        listener3.registerLockSemaphoreHolder(shutdownLockName, createCountingHolder(latch3));
        
        // Verify listeners are working by sending messages
        redisTemplate.convertAndSend(KEY_PREFIX + bucket1 + ":__unlock_channels__:{" + UNIQUE_ID + "}:" + shutdownLockName,
                UnlockType.REENTRANT_FULLY_RELEASED.name());
        redisTemplate.convertAndSend(KEY_PREFIX + bucket2 + ":__unlock_channels__:{" + UNIQUE_ID + "}:" + shutdownLockName,
                UnlockType.REENTRANT_FULLY_RELEASED.name());
        redisTemplate.convertAndSend(KEY_PREFIX + bucket3 + ":__unlock_channels__:{" + UNIQUE_ID + "}:" + shutdownLockName,
                UnlockType.REENTRANT_FULLY_RELEASED.name());
        
        // Wait for all latches to be counted down
        assertThat(latch1.await(5, TimeUnit.SECONDS)).isTrue();
        assertThat(latch2.await(5, TimeUnit.SECONDS)).isTrue();
        assertThat(latch3.await(5, TimeUnit.SECONDS)).isTrue();
        
        // Execute shutdown
        manager.shutdown();
        
        // Create new latches to verify listeners no longer work
        CountDownLatch postShutdownLatch1 = new CountDownLatch(1);
        CountDownLatch postShutdownLatch2 = new CountDownLatch(1);
        CountDownLatch postShutdownLatch3 = new CountDownLatch(1);
        
        // Try to register new semaphore holders (should not work after shutdown)
        try {
            listener1.registerLockSemaphoreHolder("new-lock", createCountingHolder(postShutdownLatch1));
            listener2.registerLockSemaphoreHolder("new-lock", createCountingHolder(postShutdownLatch2));
            listener3.registerLockSemaphoreHolder("new-lock", createCountingHolder(postShutdownLatch3));
        } catch (Exception e) {
            // Ignore exceptions - listeners might be in invalid state after shutdown
        }
        
        // Send messages again
        redisTemplate.convertAndSend(KEY_PREFIX + bucket1 + ":__unlock_channels__:{" + UNIQUE_ID + "}:new-lock",
                UnlockType.REENTRANT_FULLY_RELEASED.name());
        redisTemplate.convertAndSend(KEY_PREFIX + bucket2 + ":__unlock_channels__:{" + UNIQUE_ID + "}:new-lock",
                UnlockType.REENTRANT_FULLY_RELEASED.name());
        redisTemplate.convertAndSend(KEY_PREFIX + bucket3 + ":__unlock_channels__:{" + UNIQUE_ID + "}:new-lock",
                UnlockType.REENTRANT_FULLY_RELEASED.name());
        
        // Verify latches are not counted down (listeners should be unregistered)
        assertThat(postShutdownLatch1.await(1, TimeUnit.SECONDS)).isFalse();
        assertThat(postShutdownLatch2.await(1, TimeUnit.SECONDS)).isFalse();
        assertThat(postShutdownLatch3.await(1, TimeUnit.SECONDS)).isFalse();
    }
    
    /**
     * Helper method to create a semaphore holder that counts down a latch when signaled
     */
    private LockSemaphoreHolder createCountingHolder(CountDownLatch latch) {
        return new LockSemaphoreHolder() {
            @Override
            public void signal() {
                super.signal();
                latch.countDown();
            }
            
            @Override
            public int getWaitersCount() {
                return 1;
            }
        };
    }

    @Test
    @DisplayName("Should handle exceptions during shutdown gracefully")
    void shouldHandleExceptionsDuringShutdownGracefully() throws InterruptedException {
        // Create a custom manager with a RedisMessageListenerContainer that throws exceptions
        RedisMessageListenerContainer problematicContainer = new RedisMessageListenerContainer() {
            @Override
            public void removeMessageListener(MessageListener listener) {
                // Throw exception when removing listener
                throw new RuntimeException("Test exception");
            }
            
            @Override
            public void addMessageListener(MessageListener listener, Topic topic) {
                // Normal behavior for adding listeners
                super.addMessageListener(listener, topic);
            }
            
            @Override
            public void afterPropertiesSet() {
                // Do nothing to avoid initialization errors
            }
            
            @Override
            public void start() {
                // Do nothing to avoid initialization errors
            }
            
            @Override
            public void stop() {
                // Do nothing to avoid initialization errors
            }
        };
        
        // Set connection factory to make the container minimally functional
        problematicContainer.setConnectionFactory(redisTemplate.getConnectionFactory());
        
        // Create a manager with the problematic container
        UnlockMessageListenerManager problematicManager =
            new UnlockMessageListenerManager(problematicContainer, unlockMessageExecutor);
        
        // Create a listener
        String exceptionBucketName = "exception-bucket-" + UNIQUE_ID;
        UnlockMessageListener listener = problematicManager.getOrCreateListenerForBucket(exceptionBucketName, KEY_PREFIX);
        
        // Verify listener was created
        assertThat(listener).isNotNull();
        
        // Execute shutdown - should not throw exception
        assertThatCode(() -> problematicManager.shutdown()).doesNotThrowAnyException();
    }

    @Test
    @DisplayName("Should handle empty bucket name")
    void shouldHandleEmptyBucketName() throws InterruptedException {
        // Execute
        UnlockMessageListener listener = manager.getOrCreateListenerForBucket("", KEY_PREFIX);

        // Verify
        assertThat(listener).isNotNull();
        assertThat(listener.getBucketName()).isEmpty();
        assertThat(listener.getChannelPattern()).isEqualTo(KEY_PREFIX + ":__unlock_channels__:*");
        
        // Verify listener works with empty bucket name
        CountDownLatch latch = new CountDownLatch(1);
        String emptyBucketLockName = "empty-bucket-lock-" + UNIQUE_ID;
        
        // Register semaphore holder
        listener.registerLockSemaphoreHolder(emptyBucketLockName, createCountingHolder(latch));
        
        // Send message to channel with empty bucket name
        String channel = KEY_PREFIX + ":__unlock_channels__:{" + UNIQUE_ID + "}:" + emptyBucketLockName;
        redisTemplate.convertAndSend(channel, UnlockType.REENTRANT_FULLY_RELEASED.name());
        
        // Verify message was received
        assertThat(latch.await(5, TimeUnit.SECONDS)).isTrue();
    }

    @Test
    @DisplayName("Should handle empty key prefix")
    void shouldHandleEmptyKeyPrefix() throws InterruptedException {
        // Execute
        UnlockMessageListener listener = manager.getOrCreateListenerForBucket(BUCKET_NAME, "");

        // Verify
        assertThat(listener).isNotNull();
        assertThat(listener.getChannelPattern()).isEqualTo(":" + BUCKET_NAME + ":__unlock_channels__:*");
        
        // Verify listener works with empty key prefix
        CountDownLatch latch = new CountDownLatch(1);
        String emptyPrefixLockName = "empty-prefix-lock-" + UNIQUE_ID;
        
        // Register semaphore holder
        listener.registerLockSemaphoreHolder(emptyPrefixLockName, createCountingHolder(latch));
        
        // Send message to channel with empty prefix
        String channel = ":" + BUCKET_NAME + ":__unlock_channels__:{" + UNIQUE_ID + "}:" + emptyPrefixLockName;
        redisTemplate.convertAndSend(channel, UnlockType.REENTRANT_FULLY_RELEASED.name());
        
        // Verify message was received
        assertThat(latch.await(5, TimeUnit.SECONDS)).isTrue();
    }
}
