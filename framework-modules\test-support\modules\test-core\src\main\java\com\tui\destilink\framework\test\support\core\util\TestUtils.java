package com.tui.destilink.framework.test.support.core.util;

import lombok.experimental.UtilityClass;

import java.util.UUID;

/**
 * Utility class providing common test helper methods.
 * <p>
 * This class contains static utility methods that are useful across different types of tests.
 * It focuses on generating deterministic identifiers that can be used for test isolation
 * and reproducibility.
 * </p>
 * <p>
 * <strong>Key features:</strong>
 * </p>
 * <ul>
 *   <li>Generation of deterministic test class IDs</li>
 *   <li>Creation of reproducible UUIDs from string inputs</li>
 * </ul>
 */
@UtilityClass
public class TestUtils {

    /**
     * Generates a deterministic ID for a test class.
     * <p>
     * This method creates a hexadecimal string representation of the hash code of the test class's
     * canonical name. This ID is used to uniquely identify test classes and isolate their resources
     * (e.g., Redis keys, database schemas) to prevent interference between concurrent test executions.
     * </p>
     * <p>
     * The generated ID is consistent across test runs for the same class, making it suitable for
     * creating isolated but stable test environments.
     * </p>
     * <p>
     * This method is used by {@link com.tui.destilink.framework.test.support.core.TestSupportCoreContextCustomizer}
     * to register the test class ID in the Spring environment.
     * </p>
     * 
     * @param testClass The test class to generate an ID for
     * @return A hexadecimal string representation of the hash code of the test class's canonical name
     * @see com.tui.destilink.framework.test.support.core.TestSupportCoreContextCustomizer
     * @see com.tui.destilink.framework.test.support.core.annotation.TestClassId
     */
    public static String generateTestClassId(Class<?> testClass) {
        return Integer.toHexString(testClass.getCanonicalName().hashCode());
    }

    /**
     * Generates a reproducible UUID from a string input.
     * <p>
     * This method creates a deterministic UUID based on the bytes of the input string.
     * The same input string will always produce the same UUID, making it useful for
     * creating reproducible test data.
     * </p>
     * <p>
     * This is particularly useful when you need unique identifiers in tests that are
     * still reproducible across test runs.
     * </p>
     * <p>
     * <strong>Usage example:</strong>
     * </p>
     * <pre>
     * // Always produces the same UUID for the same input
     * UUID id1 = TestUtils.generateReproducibleUUID("test-1");
     * UUID id2 = TestUtils.generateReproducibleUUID("test-2");
     * 
     * // Can be used to create reproducible test data
     * TestEntity entity = new TestEntity();
     * entity.setId(TestUtils.generateReproducibleUUID("entity-1"));
     * </pre>
     * 
     * @param source The source string to generate the UUID from
     * @return A UUID that is deterministically generated from the input string
     */
    public static UUID generateReproducibleUUID(String source) {
        return UUID.nameUUIDFromBytes(source.getBytes());
    }

}
