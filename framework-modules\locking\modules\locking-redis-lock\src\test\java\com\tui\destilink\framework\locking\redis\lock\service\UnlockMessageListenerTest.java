package com.tui.destilink.framework.locking.redis.lock.service;

import com.tui.destilink.framework.locking.redis.lock.model.UnlockType;
import com.tui.destilink.framework.locking.redis.lock.test.TestApplication;
import com.tui.destilink.framework.test.support.core.util.TestUtils;
import com.tui.destilink.framework.test.support.redis.annotation.RedisTestSupport;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.listener.ChannelTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.test.annotation.DirtiesContext;

import java.nio.charset.StandardCharsets;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import static org.assertj.core.api.Assertions.*;

/**
 * Test suite for {@link UnlockMessageListener}.
 * <p>
 * Tests ensure that:
 * - Message parsing and processing works correctly
 * - Different unlock types trigger appropriate signaling behavior
 * - Error handling for invalid messages
 * - Asynchronous processing using provided executor
 * - Proper semaphore holder management
 * </p>
 */
@RedisTestSupport(keyspacePrefixes = { "test-unlock:" })
@SpringBootTest(classes = TestApplication.class, webEnvironment = SpringBootTest.WebEnvironment.NONE)
@DirtiesContext(classMode = DirtiesContext.ClassMode.BEFORE_EACH_TEST_METHOD)
class UnlockMessageListenerTest {

    private static final String UNIQUE_ID = TestUtils.generateTestClassId(UnlockMessageListenerTest.class);
    private static final String BUCKET_NAME = "test-bucket-" + UNIQUE_ID;
    // Use proper hash tag for Redis cluster compatibility and correct prefix for authentication
    private static final String KEY_PREFIX = UNIQUE_ID + ":test-unlock:";
    private static final String LOCK_NAME = "test-lock-" + UNIQUE_ID;
    private static final String CHANNEL_PATTERN = KEY_PREFIX + BUCKET_NAME + ":__unlock_channels__:*";
    private static final String CHANNEL = KEY_PREFIX + BUCKET_NAME + ":__unlock_channels__:{" + UNIQUE_ID + "}:" + LOCK_NAME;

    @Autowired
    private RedisConnectionFactory connectionFactory;
    
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    
    @Autowired
    private RedisMessageListenerContainer listenerContainer;
    
    private Executor unlockMessageExecutor;
    private UnlockMessageListener listener;

    @BeforeEach
    void setUp() {
        // Use a direct executor for synchronous testing
        unlockMessageExecutor = Runnable::run;
        
        // Create the listener with the test bucket and channel pattern
        listener = new UnlockMessageListener(BUCKET_NAME, CHANNEL_PATTERN, unlockMessageExecutor);
    }

    @Test
    @DisplayName("Should create listener with correct properties")
    void shouldCreateListenerWithCorrectProperties() {
        assertThat(listener.getBucketName()).isEqualTo(BUCKET_NAME);
        assertThat(listener.getChannelPattern()).isEqualTo(CHANNEL_PATTERN);
    }

    @Test
    @DisplayName("Should register and unregister semaphore holders")
    void shouldRegisterAndUnregisterSemaphoreHolders() {
        // Create a semaphore holder
        LockSemaphoreHolder semaphoreHolder = new LockSemaphoreHolder();
        
        // Register semaphore holder
        LockSemaphoreHolder registered = listener.registerLockSemaphoreHolder(LOCK_NAME, semaphoreHolder);
        
        assertThat(registered).isSameAs(semaphoreHolder);
        
        // Unregister semaphore holder
        LockSemaphoreHolder unregistered = listener.unregisterLockSemaphoreHolder(LOCK_NAME);
        
        assertThat(unregistered).isSameAs(semaphoreHolder);
        
        // Second unregister should return null
        LockSemaphoreHolder secondUnregister = listener.unregisterLockSemaphoreHolder(LOCK_NAME);
        assertThat(secondUnregister).isNull();
    }

    @Test
    @DisplayName("Should process unlock message asynchronously")
    void shouldProcessUnlockMessageAsynchronously() throws InterruptedException {
        // Setup - use a real executor for async testing
        Executor realExecutor = Executors.newSingleThreadExecutor();
        UnlockMessageListener realListener = new UnlockMessageListener(BUCKET_NAME, CHANNEL_PATTERN, realExecutor);
        
        // Create a countdown latch to wait for signal
        CountDownLatch latch = new CountDownLatch(1);
        
        // Create a semaphore holder that counts down the latch when signaled
        LockSemaphoreHolder semaphoreHolder = new LockSemaphoreHolder() {
            @Override
            public void signal() {
                super.signal();
                latch.countDown();
            }
        };
        
        // Register the semaphore holder
        realListener.registerLockSemaphoreHolder(LOCK_NAME, semaphoreHolder);
        
        // Register the listener with the container
        listenerContainer.addMessageListener(realListener, new ChannelTopic(CHANNEL));
        
        // Publish an unlock message
        redisTemplate.convertAndSend(CHANNEL, UnlockType.REENTRANT_FULLY_RELEASED.name());
        
        // Wait for the latch to be counted down
        assertThat(latch.await(5, TimeUnit.SECONDS)).isTrue();
    }

    @Test
    @DisplayName("Should signal single waiter for single-waiter unlock types")
    void shouldSignalSingleWaiterForSingleWaiterUnlockTypes() throws InterruptedException {
        // Test all single-waiter unlock types
        UnlockType[] singleWaiterTypes = {
            UnlockType.REENTRANT_FULLY_RELEASED,
            UnlockType.NON_REENTRANT_RELEASED,
            UnlockType.STATE_LOCK_RELEASED_STATE_UNCHANGED,
            UnlockType.STATE_LOCK_RELEASED_STATE_UPDATED,
            UnlockType.RW_READ_RELEASED_WAKEN_SINGLE_WRITER,
            UnlockType.STAMPED_WRITE_RELEASED,
            UnlockType.STAMPED_CONVERTED_TO_READ,
            UnlockType.STAMPED_CONVERTED_TO_WRITE
        };
        
        for (UnlockType unlockType : singleWaiterTypes) {
            testUnlockTypeSignaling(unlockType, 1, 1);
        }
    }

    @Test
    @DisplayName("Should signal all waiters for multi-waiter unlock types")
    void shouldSignalAllWaitersForMultiWaiterUnlockTypes() throws InterruptedException {
        // Test all multi-waiter unlock types
        UnlockType[] multiWaiterTypes = {
            UnlockType.RW_READ_RELEASED_WAKEN_READERS,
            UnlockType.RW_WRITE_RELEASED_WAKEN_ALL,
            UnlockType.STAMPED_READ_RELEASED
        };
        
        for (UnlockType unlockType : multiWaiterTypes) {
            testUnlockTypeSignaling(unlockType, 5, 5); // 5 waiters, signal all 5
        }
    }

    private void testUnlockTypeSignaling(UnlockType unlockType, int waitersCount, int expectedSignals) throws InterruptedException {
        // Create a unique lock name for this test to avoid interference
        String testLockName = LOCK_NAME + "-" + unlockType.name();
        String testChannel = KEY_PREFIX + BUCKET_NAME + ":__unlock_channels__:{" + UNIQUE_ID + "}:" + testLockName;
        
        // Create a countdown latch to wait for signals
        CountDownLatch latch = new CountDownLatch(expectedSignals);
        
        // Create a semaphore holder that counts down the latch when signaled
        LockSemaphoreHolder semaphoreHolder = new LockSemaphoreHolder() {
            @Override
            public void signal() {
                super.signal();
                latch.countDown();
            }
            
            @Override
            public void signal(int permits) {
                super.signal(permits);
                for (int i = 0; i < permits; i++) {
                    latch.countDown();
                }
            }
            
            @Override
            public int getWaitersCount() {
                return waitersCount;
            }
        };
        
        // Register the semaphore holder
        listener.registerLockSemaphoreHolder(testLockName, semaphoreHolder);
        
        // Register the listener with the container
        listenerContainer.addMessageListener(listener, new ChannelTopic(testChannel));
        
        // Publish an unlock message
        redisTemplate.convertAndSend(testChannel, unlockType.name());
        
        // Wait for the latch to be counted down
        assertThat(latch.await(5, TimeUnit.SECONDS)).isTrue();
        
        // Unregister the listener to avoid interference with other tests
        listenerContainer.removeMessageListener(listener, new ChannelTopic(testChannel));
        listener.unregisterLockSemaphoreHolder(testLockName);
    }

    @Test
    @DisplayName("Should handle invalid unlock type gracefully")
    void shouldHandleInvalidUnlockTypeGracefully() throws InterruptedException {
        // Create a unique lock name for this test
        String testLockName = LOCK_NAME + "-invalid";
        String testChannel = KEY_PREFIX + BUCKET_NAME + ":__unlock_channels__:{" + UNIQUE_ID + "}:" + testLockName;
        
        // Create a countdown latch that should NOT be counted down
        CountDownLatch latch = new CountDownLatch(1);
        
        // Create a semaphore holder that counts down the latch when signaled
        // This should never happen with invalid message
        LockSemaphoreHolder semaphoreHolder = new LockSemaphoreHolder() {
            @Override
            public void signal() {
                super.signal();
                latch.countDown();
            }
            
            @Override
            public int getWaitersCount() {
                return 1;
            }
        };
        
        // Register the semaphore holder
        listener.registerLockSemaphoreHolder(testLockName, semaphoreHolder);
        
        // Register the listener with the container
        listenerContainer.addMessageListener(listener, new ChannelTopic(testChannel));
        
        // Publish an invalid unlock message
        redisTemplate.convertAndSend(testChannel, "INVALID_UNLOCK_TYPE");
        
        // Wait briefly - the latch should NOT be counted down
        assertThat(latch.await(1, TimeUnit.SECONDS)).isFalse();
        
        // Unregister the listener to avoid interference with other tests
        listenerContainer.removeMessageListener(listener, new ChannelTopic(testChannel));
        listener.unregisterLockSemaphoreHolder(testLockName);
    }

    @Test
    @DisplayName("Should handle zero waiters gracefully")
    void shouldHandleZeroWaitersGracefully() throws InterruptedException {
        // Create a unique lock name for this test
        String testLockName = LOCK_NAME + "-zero-waiters";
        String testChannel = KEY_PREFIX + BUCKET_NAME + ":__unlock_channels__:{" + UNIQUE_ID + "}:" + testLockName;
        
        // Create a countdown latch that should NOT be counted down
        CountDownLatch latch = new CountDownLatch(1);
        
        // Create a semaphore holder with zero waiters that counts down the latch if signaled
        // This should never happen with zero waiters
        LockSemaphoreHolder semaphoreHolder = new LockSemaphoreHolder() {
            @Override
            public void signal() {
                super.signal();
                latch.countDown();
            }
            
            @Override
            public void signal(int permits) {
                super.signal(permits);
                latch.countDown();
            }
            
            @Override
            public int getWaitersCount() {
                return 0; // Zero waiters
            }
        };
        
        // Register the semaphore holder
        listener.registerLockSemaphoreHolder(testLockName, semaphoreHolder);
        
        // Register the listener with the container
        listenerContainer.addMessageListener(listener, new ChannelTopic(testChannel));
        
        // Publish an unlock message
        redisTemplate.convertAndSend(testChannel, UnlockType.REENTRANT_FULLY_RELEASED.name());
        
        // Wait briefly - the latch should NOT be counted down
        assertThat(latch.await(1, TimeUnit.SECONDS)).isFalse();
        
        // Unregister the listener to avoid interference with other tests
        listenerContainer.removeMessageListener(listener, new ChannelTopic(testChannel));
        listener.unregisterLockSemaphoreHolder(testLockName);
    }

    @Test
    @DisplayName("Should extract lock name from channel correctly")
    void shouldExtractLockNameFromChannelCorrectly() throws InterruptedException {
        // Test various channel formats with different lock name patterns
        String[] lockNameFormats = {
            "my-lock-" + UNIQUE_ID,
            "another-lock-" + UNIQUE_ID,
            "lock:name:with:colons-" + UNIQUE_ID
        };

        // Test each lock name format
        for (String lockName : lockNameFormats) {
            // Create a channel for this lock name
            String testChannel = KEY_PREFIX + BUCKET_NAME + ":__unlock_channels__:{" + UNIQUE_ID + "}:" + lockName;
            
            // Create a countdown latch to wait for signal
            CountDownLatch latch = new CountDownLatch(1);
            
            // Create a semaphore holder that counts down the latch when signaled
            LockSemaphoreHolder semaphoreHolder = new LockSemaphoreHolder() {
                @Override
                public void signal() {
                    super.signal();
                    latch.countDown();
                }
                
                @Override
                public int getWaitersCount() {
                    return 1;
                }
            };
            
            // Register the semaphore holder with the specific lock name
            listener.registerLockSemaphoreHolder(lockName, semaphoreHolder);
            
            // Register the listener with the container for this channel
            listenerContainer.addMessageListener(listener, new ChannelTopic(testChannel));
            
            // Publish an unlock message to this channel
            redisTemplate.convertAndSend(testChannel, UnlockType.REENTRANT_FULLY_RELEASED.name());
            
            // Wait for the latch to be counted down - this verifies the correct semaphore was signaled
            assertThat(latch.await(5, TimeUnit.SECONDS)).isTrue();
            
            // Unregister the listener to avoid interference with other tests
            listenerContainer.removeMessageListener(listener, new ChannelTopic(testChannel));
            listener.unregisterLockSemaphoreHolder(lockName);
        }
    }
}
