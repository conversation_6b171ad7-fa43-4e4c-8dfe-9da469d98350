-- hget.lua
-- This script performs a HGET operation and stores the result in a response cache.
-- It implements idempotency by checking the response cache first.
--
-- KEYS[1] - The hash key
-- KEYS[2] - The response cache key
-- 
-- ARGV[1] - The request UUID for idempotency
-- ARGV[2] - The field to get from the hash
-- ARGV[3] - The TTL for the response cache in seconds

-- Check if response cache exists first for idempotency
local cached = redis.call('GET', KEYS[2])
if cached then
    return cached
end

-- Perform the HGET operation
local result = redis.call('HGET', KEYS[1], ARGV[2])

-- Store the result in the response cache with TTL
redis.call('SETEX', KEYS[2], ARGV[3], result or "")

return result
