package com.tui.destilink.framework.locking.redis.lock.test;

import com.tui.destilink.framework.test.support.redis.annotation.RedisTestSupport;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.NoSuchBeanDefinitionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;

import java.util.Arrays;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertTrue;

@SpringBootTest(classes = TestApplication.class)
@RedisTestSupport
public class DebugRedisConnectionFactoryTest {

    @Autowired
    private ApplicationContext applicationContext;

    @Test
    public void shouldHaveRedisConnectionFactoryBean() {
        // Check if any RedisConnectionFactory beans exist
        Map<String, RedisConnectionFactory> beans = applicationContext.getBeansOfType(RedisConnectionFactory.class);
        
        System.out.println("RedisConnectionFactory beans found:");
        beans.forEach((name, bean) -> {
            System.out.println("  - Bean name: " + name + ", Bean class: " + bean.getClass().getName());
        });
        
        // Should have at least one RedisConnectionFactory bean
        assertTrue(beans.size() > 0, "Expected at least one RedisConnectionFactory bean");
        
        // Check if a specific bean named 'redisConnectionFactory' exists
        if (beans.containsKey("redisConnectionFactory")) {
            System.out.println("✅ Found bean named 'redisConnectionFactory'");
        } else {
            System.out.println("❌ No bean named 'redisConnectionFactory' found");
        }
    }

    @Test
    public void shouldListAllBeansWithRedisInName() {
        String[] allBeanNames = applicationContext.getBeanDefinitionNames();
        
        System.out.println("All beans with 'redis' in name:");
        Arrays.stream(allBeanNames)
                .filter(name -> name.toLowerCase().contains("redis"))
                .sorted()
                .forEach(name -> {
                    Object bean;
                    try {
                        bean = applicationContext.getBean(name);
                        System.out.println("  - " + name + " : " + bean.getClass().getName());
                    } catch (Exception e) {
                        System.out.println("  - " + name + " : ERROR getting bean - " + e.getMessage());
                    }
                });
    }
    
    @Test
    public void shouldHaveRedisMessageListenerContainer() {
        try {
            RedisMessageListenerContainer container = applicationContext.getBean(RedisMessageListenerContainer.class);
            System.out.println("✅ RedisMessageListenerContainer is present: " + container.getClass().getName());
            assertTrue(true, "RedisMessageListenerContainer found");
        } catch (NoSuchBeanDefinitionException e) {
            System.out.println("❌ RedisMessageListenerContainer is missing: " + e.getMessage());
            
            // Check what RedisConnectionFactory beans are available
            Map<String, RedisConnectionFactory> connectionFactories = applicationContext.getBeansOfType(RedisConnectionFactory.class);
            System.out.println("Available RedisConnectionFactory beans for RedisMessageListenerContainer:");
            connectionFactories.forEach((name, bean) -> {
                System.out.println("  - " + name + " : " + bean.getClass().getName());
            });
            
            throw e;
        }
    }
}
