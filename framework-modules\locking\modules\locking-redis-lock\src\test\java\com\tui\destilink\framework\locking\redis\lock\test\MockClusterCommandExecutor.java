package com.tui.destilink.framework.locking.redis.lock.test;

import com.tui.destilink.framework.redis.core.cluster.ClusterCommandExecutor;
import com.tui.destilink.framework.redis.core.cluster.pool.BlockingClusterConnectionPool;
import com.tui.destilink.framework.redis.core.cluster.pool.CycleClusterConnectionPool;
import com.tui.destilink.framework.redis.core.config.RedisCoreProperties;
import com.tui.destilink.framework.redis.core.script.ImmutableLettuceScript;
import io.lettuce.core.RedisFuture;
import io.lettuce.core.cluster.api.async.AsyncExecutions;
import io.lettuce.core.cluster.api.async.NodeSelectionAsyncCommands;
import io.lettuce.core.cluster.api.async.RedisClusterAsyncCommands;
import io.lettuce.core.cluster.models.partitions.RedisClusterNode;
import io.micrometer.core.instrument.MeterRegistry;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.connection.ReturnType;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Predicate;

/**
 * Mock implementation of ClusterCommandExecutor for testing with standalone Redis.
 * This provides the minimal functionality needed for Redis lock operations to work in tests.
 */
public class MockClusterCommandExecutor extends ClusterCommandExecutor {
    
    private final RedisTemplate<String, String> redisTemplate;
    
    public MockClusterCommandExecutor(RedisConnectionFactory connectionFactory) {
        super((BlockingClusterConnectionPool) null, (CycleClusterConnectionPool) null, (RedisCoreProperties) null);
        
        // Create a RedisTemplate for standalone operations
        this.redisTemplate = new RedisTemplate<>();
        this.redisTemplate.setConnectionFactory(connectionFactory);
        this.redisTemplate.setKeySerializer(org.springframework.data.redis.serializer.StringRedisSerializer.UTF_8);
        this.redisTemplate.setValueSerializer(org.springframework.data.redis.serializer.StringRedisSerializer.UTF_8);
        this.redisTemplate.setHashKeySerializer(org.springframework.data.redis.serializer.StringRedisSerializer.UTF_8);
        this.redisTemplate.setHashValueSerializer(org.springframework.data.redis.serializer.StringRedisSerializer.UTF_8);
        this.redisTemplate.afterPropertiesSet();
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        // Override to prevent calling super which would fail with null dependencies
    }

    @Override
    public void destroy() {
        // Override to prevent calling super which would fail with null dependencies
    }

    @Override
    public void bindTo(MeterRegistry registry) {
        // No-op for tests
    }

    @Override
    public <T> CompletableFuture<T> execute(Function<RedisClusterAsyncCommands<String, String>, RedisFuture<T>> function) {
        throw new UnsupportedOperationException("For tests, use executeScript method for Lua script execution");
    }

    @Override
    public <T> CompletableFuture<T> executeOnNode(String key, Function<RedisClusterAsyncCommands<String, String>, RedisFuture<T>> function) {
        throw new UnsupportedOperationException("For tests, use executeScript method for Lua script execution");
    }

    @Override
    public <T> CompletableFuture<T> executeOnNode(int slot, Function<RedisClusterAsyncCommands<String, String>, RedisFuture<T>> function) {
        throw new UnsupportedOperationException("For tests, use executeScript method for Lua script execution");
    }

    @Override
    public <T> CompletableFuture<List<T>> executeOnAllNodes(Function<NodeSelectionAsyncCommands<String, String>, AsyncExecutions<T>> function) {
        return CompletableFuture.completedFuture(List.of());
    }

    @Override
    public <T> CompletableFuture<List<T>> executeOnAllUpstream(Function<NodeSelectionAsyncCommands<String, String>, AsyncExecutions<T>> function) {
        return CompletableFuture.completedFuture(List.of());
    }

    @Override
    public <T> CompletableFuture<List<T>> executeOnAllReplicas(Function<NodeSelectionAsyncCommands<String, String>, AsyncExecutions<T>> function) {
        return CompletableFuture.completedFuture(List.of());
    }

    @Override
    public <T> CompletableFuture<List<T>> executeOnAllFiltered(Predicate<RedisClusterNode> filter, boolean dynamic, Function<NodeSelectionAsyncCommands<String, String>, AsyncExecutions<T>> function) {
        return CompletableFuture.completedFuture(List.of());
    }

    @Override
    public CompletableFuture<Void> executeInPipeline(Consumer<RedisClusterAsyncCommands<String, String>> consumer) {
        return CompletableFuture.completedFuture(null);
    }

    @Override
    public <T> CompletableFuture<T> executeInPipeline(Function<RedisClusterAsyncCommands<String, String>, RedisFuture<T>> function) {
        throw new UnsupportedOperationException("For tests, use executeScript method for Lua script execution");
    }

    /**
     * Override the script execution method to handle Lua scripts for tests.
     * This is the key method that the Redis lock operations use.
     */
    @Override
    public <T> CompletableFuture<T> executeScript(String key, ImmutableLettuceScript<T> script, String[] keys, String[] args) {
        try {
            // Execute the Lua script using RedisTemplate
            @SuppressWarnings("unchecked")
            T result = redisTemplate.execute((RedisCallback<T>) connection -> {
                byte[][] keysAndArgs = concatenateKeysAndArgs(keys, args);
                // Use the correct return type from the script
                ReturnType returnType = script.getReturnType();
                Object scriptResult = connection.scriptingCommands().eval(script.getScriptAsString().getBytes(), returnType, keys.length, keysAndArgs);
                return (T) scriptResult;
            });
            return CompletableFuture.completedFuture(result);
        } catch (Exception e) {
            return CompletableFuture.failedFuture(e);
        }
    }

    private byte[][] concatenateKeysAndArgs(String[] keys, String[] args) {
        byte[][] result = new byte[keys.length + args.length][];
        int i = 0;
        for (String key : keys) {
            result[i++] = key.getBytes();
        }
        for (String arg : args) {
            result[i++] = arg.getBytes();
        }
        return result;
    }
}
