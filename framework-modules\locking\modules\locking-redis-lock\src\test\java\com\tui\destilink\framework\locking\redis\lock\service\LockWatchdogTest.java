package com.tui.destilink.framework.locking.redis.lock.service;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatCode;

import java.time.Duration;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.Executors;

import org.awaitility.Awaitility;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.util.ReflectionTestUtils;

import com.tui.destilink.framework.locking.redis.lock.config.RedisLockProperties;
import com.tui.destilink.framework.locking.redis.lock.test.TestApplication;
import com.tui.destilink.framework.test.support.core.util.TestUtils;

import com.tui.destilink.framework.test.support.core.util.TestUtils;
import com.tui.destilink.framework.test.support.redis.annotation.RedisTestSupport;

/**
 * Test suite for {@link LockWatchdog} behavior.
 * <p>
 * Tests ensure that:
 * - Watchdog is always active when module is enabled
 * - Conditional monitoring based on safety buffer calculation
 * - Proper lease extension without modifying originalLeaseTimeMillis
 * - Correct handling of lock expiration and unregistration
 * - Integration with RedisLockOperations for retries
 * </p>
 */
@RedisTestSupport(keyspacePrefixes = { "test-watchdog:" })
@SpringBootTest(classes = TestApplication.class, webEnvironment = SpringBootTest.WebEnvironment.NONE)
@DirtiesContext(classMode = DirtiesContext.ClassMode.BEFORE_EACH_TEST_METHOD)
class LockWatchdogTest {

    private static final String UNIQUE_ID = TestUtils.generateTestClassId(LockWatchdogTest.class);

    @Autowired
    private LockWatchdog lockWatchdog;

    @Autowired
    private RedisLockOperations redisLockOperations;

    @Autowired
    private LockOwnerSupplier lockOwnerSupplier;

    @Autowired
    private RedisLockProperties redisLockProperties;

    private String lockKey;
    private String ownerId;

    @BeforeEach
    void setUp() {
        // Use proper hash tag for Redis cluster compatibility and correct prefix for authentication
        lockKey = UNIQUE_ID + ":test-watchdog:{" + UNIQUE_ID + "}:lock";
        ownerId = lockOwnerSupplier.get();
    }

    @Test
    @DisplayName("Should be active when module is enabled")
    void shouldBeActiveWhenModuleEnabled() {
        // Watchdog should be instantiated and available
        assertThat(lockWatchdog).isNotNull();
        
        // Should be able to register locks (even if they're not monitored)
        Duration leaseTime = Duration.ofSeconds(10);
        LockWatchdog.WatchdogHandle handle = lockWatchdog.registerLock(lockKey, ownerId, leaseTime);
        
        assertThat(handle).isNotNull();
        
        // Clean up
        handle.unregister();
    }

    @Test
    @DisplayName("Should register and unregister locks correctly")
    void shouldRegisterAndUnregisterLocks() {
        Duration leaseTime = Duration.ofSeconds(30);
        
        // Register lock
        LockWatchdog.WatchdogHandle handle = lockWatchdog.registerLock(lockKey, ownerId, leaseTime);
        assertThat(handle).isNotNull();
        
        // Unregister using handle
        assertThatCode(() -> handle.unregister()).doesNotThrowAnyException();
        
        // Multiple unregistrations should be safe
        assertThatCode(() -> handle.unregister()).doesNotThrowAnyException();
    }

    @Test
    @DisplayName("Should unregister locks directly")
    void shouldUnregisterLocksDirectly() {
        Duration leaseTime = Duration.ofSeconds(30);
        
        // Register lock
        lockWatchdog.registerLock(lockKey, ownerId, leaseTime);
        
        // Unregister directly
        assertThatCode(() -> lockWatchdog.unregisterLock(lockKey, ownerId))
                .doesNotThrowAnyException();
        
        // Multiple unregistrations should be safe
        assertThatCode(() -> lockWatchdog.unregisterLock(lockKey, ownerId))
                .doesNotThrowAnyException();
    }

    @Test
    @DisplayName("Should handle lock extension through RedisLockOperations")
    void shouldHandleLockExtensionThroughRedisLockOperations() {
        Duration leaseTime = Duration.ofSeconds(30);
        
        // First acquire the lock in Redis
        CompletableFuture<Boolean> lockAcquired = redisLockOperations.acquireLock(lockKey, ownerId, leaseTime);
        assertThat(lockAcquired).succeedsWithin(Duration.ofSeconds(5))
                .isEqualTo(true);
        
        // Register with watchdog
        LockWatchdog.WatchdogHandle handle = lockWatchdog.registerLock(lockKey, ownerId, leaseTime);
        
        // Wait a bit to allow potential watchdog activity
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // Lock should still be held (watchdog should maintain it)
        CompletableFuture<Boolean> isLocked = redisLockOperations.isLocked(lockKey);
        assertThat(isLocked).succeedsWithin(Duration.ofSeconds(5))
                .isEqualTo(true);
        
        // Clean up
        handle.unregister();
        redisLockOperations.unlock(lockKey, ownerId).join();
    }

    @Test
    @DisplayName("Should handle watchdog configuration properties")
    void shouldHandleWatchdogConfigurationProperties() {
        RedisLockProperties.WatchdogProperties watchdogProps = redisLockProperties.getWatchdog();
        
        // Verify configuration is loaded
        assertThat(watchdogProps).isNotNull();
        assertThat(watchdogProps.getInterval()).isNotNull();
        assertThat(watchdogProps.getFactor()).isGreaterThan(0.0);
        assertThat(watchdogProps.getCorePoolSize()).isGreaterThan(0);
        assertThat(watchdogProps.getThreadNamePrefix()).isNotBlank();
        assertThat(watchdogProps.getShutdownAwaitTermination()).isNotNull();
    }

    @Test
    @DisplayName("Should handle multiple concurrent registrations")
    void shouldHandleMultipleConcurrentRegistrations() {
        Duration leaseTime = Duration.ofSeconds(30);
        
        // Register multiple locks
        String lockKey1 = lockKey + ":1";
        String lockKey2 = lockKey + ":2";
        String lockKey3 = lockKey + ":3";
        
        LockWatchdog.WatchdogHandle handle1 = lockWatchdog.registerLock(lockKey1, ownerId, leaseTime);
        LockWatchdog.WatchdogHandle handle2 = lockWatchdog.registerLock(lockKey2, ownerId, leaseTime);
        LockWatchdog.WatchdogHandle handle3 = lockWatchdog.registerLock(lockKey3, ownerId, leaseTime);
        
        assertThat(handle1).isNotNull();
        assertThat(handle2).isNotNull();
        assertThat(handle3).isNotNull();
        
        // Unregister all
        handle1.unregister();
        handle2.unregister();
        handle3.unregister();
    }

    @Test
    @DisplayName("Should handle shutdown gracefully")
    void shouldHandleShutdownGracefully() {
        Duration leaseTime = Duration.ofSeconds(30);
        
        // Register a lock
        LockWatchdog.WatchdogHandle handle = lockWatchdog.registerLock(lockKey, ownerId, leaseTime);
        
        // Shutdown should not throw exceptions
        assertThatCode(() -> lockWatchdog.shutdown()).doesNotThrowAnyException();
        
        // Clean up (should still work after shutdown)
        assertThatCode(() -> handle.unregister()).doesNotThrowAnyException();
    }

    @Test
    @DisplayName("Should handle different lease durations")
    void shouldHandleDifferentLeaseDurations() {
        // Test with various lease durations
        Duration shortLease = Duration.ofSeconds(5);
        Duration mediumLease = Duration.ofSeconds(30);
        Duration longLease = Duration.ofMinutes(5);
        
        LockWatchdog.WatchdogHandle handle1 = lockWatchdog.registerLock(lockKey + ":short", ownerId, shortLease);
        LockWatchdog.WatchdogHandle handle2 = lockWatchdog.registerLock(lockKey + ":medium", ownerId, mediumLease);
        LockWatchdog.WatchdogHandle handle3 = lockWatchdog.registerLock(lockKey + ":long", ownerId, longLease);
        
        assertThat(handle1).isNotNull();
        assertThat(handle2).isNotNull();
        assertThat(handle3).isNotNull();
        
        // Clean up
        handle1.unregister();
        handle2.unregister();
        handle3.unregister();
    }

    @Test
    @DisplayName("Should only monitor locks with leaseTime greater than safety buffer")
    void shouldOnlyMonitorLocksWithLeaseTimeGreaterThanSafetyBuffer() {
        RedisLockProperties.WatchdogProperties watchdogProps = redisLockProperties.getWatchdog();
        long safetyBufferMillis = (long) (watchdogProps.getInterval().toMillis() * watchdogProps.getFactor());

        // Eligible for monitoring
        Duration eligibleLeaseTime = Duration.ofMillis(safetyBufferMillis + 100);
        lockWatchdog.registerLock(lockKey + ":eligible", ownerId, eligibleLeaseTime);
        ConcurrentMap<String, ?> watchedLocks = (ConcurrentMap<String, ?>) ReflectionTestUtils.getField(lockWatchdog, "watchedLocks");
        assertThat(watchedLocks).containsKey(lockKey + ":eligible:" + ownerId);

        // Not eligible for monitoring
        Duration ineligibleLeaseTime = Duration.ofMillis(safetyBufferMillis);
        lockWatchdog.registerLock(lockKey + ":ineligible", ownerId, ineligibleLeaseTime);
        assertThat(watchedLocks).doesNotContainKey(lockKey + ":ineligible:" + ownerId);
    }

    @Test
    @DisplayName("Should not extend lock with short leaseTime (not eligible for watchdog)")
    void shouldNotExtendLockWithShortLeaseTime() {
        RedisLockProperties.WatchdogProperties watchdogProps = redisLockProperties.getWatchdog();
        long safetyBufferMillis = (long) (watchdogProps.getInterval().toMillis() * watchdogProps.getFactor());
        Duration shortLease = Duration.ofMillis(safetyBufferMillis);

        // Acquire lock
        assertThat(redisLockOperations.acquireLock(lockKey, ownerId, shortLease).join()).isTrue();

        // Register with watchdog (should not be monitored)
        lockWatchdog.registerLock(lockKey, ownerId, shortLease);

        // Verify it is not monitored
        ConcurrentMap<String, ?> watchedLocks = (ConcurrentMap<String, ?>) ReflectionTestUtils.getField(lockWatchdog, "watchedLocks");
        assertThat(watchedLocks).doesNotContainKey(lockKey + ":" + ownerId);

        // Wait for the lease to expire
        Awaitility.await().atMost(shortLease.plus(Duration.ofSeconds(1))).untilAsserted(() -> {
            assertThat(redisLockOperations.isLocked(lockKey).join()).isFalse();
        });
    }

    @Test
    @DisplayName("Should extend lock with long leaseTime multiple times")
    void shouldExtendLockWithLongLeaseTime() {
        RedisLockProperties.WatchdogProperties watchdogProps = redisLockProperties.getWatchdog();
        long safetyBufferMillis = (long) (watchdogProps.getInterval().toMillis() * watchdogProps.getFactor());
        Duration longLease = Duration.ofMillis(safetyBufferMillis + 2000); // Eligible

        // Acquire lock
        assertThat(redisLockOperations.acquireLock(lockKey, ownerId, longLease).join()).isTrue();
        long initialTtl = redisLockOperations.getTtl(lockKey).join();

        // Register with watchdog
        LockWatchdog.WatchdogHandle handle = lockWatchdog.registerLock(lockKey, ownerId, longLease);

        // Wait for watchdog to extend the lock
        Awaitility.await().atMost(watchdogProps.getInterval().plus(Duration.ofSeconds(2))).untilAsserted(() -> {
            long newTtl = redisLockOperations.getTtl(lockKey).join();
            assertThat(newTtl).isGreaterThan(initialTtl);
        });

        // Clean up
        handle.unregister();
        redisLockOperations.unlock(lockKey, ownerId).join();
    }

    @Test
    @DisplayName("Should handle final leg extension correctly")
    void shouldHandleFinalLegExtensionCorrectly() throws InterruptedException {
        // Calculate safety buffer from watchdog properties
        RedisLockProperties.WatchdogProperties watchdogProps = redisLockProperties.getWatchdog();
        long safetyBufferMillis = (long) (watchdogProps.getInterval().toMillis() * watchdogProps.getFactor());
        
        // Set lease time to be slightly longer than safety buffer
        // The final leg logic kicks in when remaining time < safetyBuffer but > 0
        long leaseTimeMillis = safetyBufferMillis + 2000; // 2 seconds longer than safety buffer
        Duration leaseTime = Duration.ofMillis(leaseTimeMillis);
        
        // First acquire the lock
        assertThat(redisLockOperations.acquireLock(lockKey, ownerId, leaseTime).join()).isTrue();
        long initialAcquisitionTime = System.currentTimeMillis();
        long expectedExpiryTime = initialAcquisitionTime + leaseTimeMillis;
        
        // Register with watchdog
        LockWatchdog.WatchdogHandle handle = lockWatchdog.registerLock(lockKey, ownerId, leaseTime);
        
        // Wait until we're in the final leg - between safety buffer and expiration
        long sleepTimeMillis = leaseTimeMillis - safetyBufferMillis - 500; // 500ms margin for test reliability
        Thread.sleep(sleepTimeMillis);
        
        // Allow watchdog to run its refresh cycle
        Thread.sleep(watchdogProps.getInterval().toMillis() + 500);
        
        // Verify the lock is still held
        assertThat(redisLockOperations.isLocked(lockKey).join()).isTrue();
        
        // Check the TTL - it should be close to the original intended expiry time
        // rather than just being extended by the safety buffer
        long newTtl = redisLockOperations.getTtl(lockKey).join();
        long currentTime = System.currentTimeMillis();
        long approximateExpectedTtl = expectedExpiryTime - currentTime;
        
        // Allow for some margin of error in timing due to test execution
        assertThat(newTtl).isGreaterThan(safetyBufferMillis / 2);
        assertThat(Math.abs(newTtl - approximateExpectedTtl)).isLessThan(1500);
        
        // Clean up
        handle.unregister();
        redisLockOperations.unlock(lockKey, ownerId).join();
    }

    @Test
    @DisplayName("Should preserve originalLeaseTimeMillis during refresh")
    void shouldPreserveOriginalLeaseTimeMillisDuringRefresh() {
        // Use the actual RedisLockOperations to verify values stored in Redis
        String lockDataKey = lockKey + ":data";
        
        Duration leaseTime = Duration.ofSeconds(10);
        long originalLeaseTimeMillis = leaseTime.toMillis();

        // Acquire lock and register
        assertThat(redisLockOperations.acquireLock(lockKey, ownerId, leaseTime).join()).isTrue();
        
        // Verify originalLeaseTimeMillis is stored in Redis hash
        String initialStoredValue = redisLockOperations.hget(lockDataKey, "originalLeaseTimeMillis").join();
        assertThat(initialStoredValue).isNotNull();
        assertThat(Long.parseLong(initialStoredValue)).isEqualTo(originalLeaseTimeMillis);
        
        // Register with watchdog
        LockWatchdog.WatchdogHandle handle = lockWatchdog.registerLock(lockKey, ownerId, leaseTime);
        
        // Wait for watchdog to run a refresh cycle
        Awaitility.await().atMost(redisLockProperties.getWatchdog().getInterval().plus(Duration.ofSeconds(1)))
            .untilAsserted(() -> {
                // Verify TTL has been extended
                long currentTtl = redisLockOperations.getTtl(lockKey).join();
                assertThat(currentTtl).isGreaterThan(0);
            });
        
        // Directly check that originalLeaseTimeMillis is still the same in Redis hash after refresh
        String refreshedStoredValue = redisLockOperations.hget(lockDataKey, "originalLeaseTimeMillis").join();
        assertThat(refreshedStoredValue).isNotNull();
        assertThat(Long.parseLong(refreshedStoredValue)).isEqualTo(originalLeaseTimeMillis);
        
        // Cleanup
        handle.unregister();
        redisLockOperations.unlock(lockKey, ownerId).join();
    }
    
    @Test
    @DisplayName("Should honor different watchdog configurations")
    void shouldHonorDifferentWatchdogConfigurations() {
        // Create a watchdog with custom properties
        RedisLockProperties.WatchdogProperties originalProps = redisLockProperties.getWatchdog();
        
        // Create a copy of the properties with different settings
        // TODO FIX RedisLockProperties.WatchdogProperties customProps = new RedisLockProperties.WatchdogProperties();
        // Use shorter interval for testing
        // customProps.setInterval(Duration.ofMillis(500)); TODO fix
        // Use higher factor, making safety buffer larger relative to interval
        // customProps.setFactor(2.0); TODO fix
        // Keep other properties same as original
        // customProps.setCorePoolSize(originalProps.getCorePoolSize()); TODO fix
        // customProps.setThreadNamePrefix(originalProps.getThreadNamePrefix()); TODO fix
        // customProps.setShutdownAwaitTermination(originalProps.getShutdownAwaitTermination());
        // TODO fix
        
        // Create custom watchdog with modified properties
        /* TODO FIX LockWatchdog customWatchdog = new LockWatchdog(
            redisLockOperations,
            lockWatchdog.getErrorHandler(),
            customProps,
            Executors.newVirtualThreadPerTaskExecutor()
        );
        
        try {
            // Calculate safety buffer for custom config
            long customSafetyBufferMillis = (long) (customProps.getInterval().toMillis() * customProps.getFactor());
            
            // Test for lock that should not be eligible for monitoring
            // With factor of 2.0 and interval of 500ms, safety buffer is 1000ms
            Duration shortLease = Duration.ofMillis(customSafetyBufferMillis);
            String shortLockKey = lockKey + ":short";
            
            // Acquire locks
            assertThat(redisLockOperations.acquireLock(shortLockKey, ownerId, shortLease).join()).isTrue();
            customWatchdog.registerLock(shortLockKey, ownerId, shortLease);
            
            // Verify it's not monitored due to safety buffer calculation
            ConcurrentMap<String, ?> watchedLocks = (ConcurrentMap<String, ?>)
                ReflectionTestUtils.getField(customWatchdog, "watchedLocks");
            assertThat(watchedLocks).doesNotContainKey(shortLockKey + ":" + ownerId);
            
            // Test for lock that should be eligible for monitoring
            // Lease time is slightly longer than safety buffer
            Duration longLease = Duration.ofMillis(customSafetyBufferMillis + 500);
            String longLockKey = lockKey + ":long";
            
            // Acquire locks
            assertThat(redisLockOperations.acquireLock(longLockKey, ownerId, longLease).join()).isTrue();
            LockWatchdog.WatchdogHandle handle = customWatchdog.registerLock(longLockKey, ownerId, longLease);
            
            // Verify it is monitored
            assertThat(watchedLocks).containsKey(longLockKey + ":" + ownerId);
            
            // Verify refresh behavior with shorter interval
            long initialTtl = redisLockOperations.getTtl(longLockKey).join();
            
            // Wait for watchdog to run with custom shorter interval
            Awaitility.await()
                .atMost(customProps.getInterval().multipliedBy(2))
                .pollInterval(Duration.ofMillis(100))
                .untilAsserted(() -> {
                    long newTtl = redisLockOperations.getTtl(longLockKey).join();
                    // With shorter interval, lock should be refreshed sooner
                    assertThat(newTtl).isGreaterThan(initialTtl - 100); // Allow small margin for test timing
                });
            
            // Clean up
            handle.unregister();
            redisLockOperations.unlock(longLockKey, ownerId).join();
            redisLockOperations.unlock(shortLockKey, ownerId).join();
            
        } finally {
            // Always shut down the custom watchdog
            customWatchdog.shutdown();
        }
            */
    }
}
