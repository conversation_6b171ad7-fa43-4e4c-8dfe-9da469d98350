package com.tui.destilink.framework.locking.redis.lock.test;

import com.tui.destilink.framework.test.support.redis.annotation.RedisTestSupport;
import com.tui.destilink.framework.test.support.redis.config.TestConfigProvider;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.data.redis.connection.RedisConnectionFactory;

/**
 * Test to debug Spring Boot configuration and bean creation.
 */
@SpringBootTest(classes = TestApplication.class)
@RedisTestSupport
class DebugConfigurationTest {

    @Autowired
    private ApplicationContext applicationContext;

    @Test
    void shouldHaveTestConfigProvider() {
        // Check if TestConfigProvider bean exists
        boolean hasTestConfigProvider = applicationContext.containsBean("TestConfigProvider");
        System.out.println("TestConfigProvider bean exists: " + hasTestConfigProvider);
        
        if (hasTestConfigProvider) {
            TestConfigProvider provider = applicationContext.getBean(TestConfigProvider.class);
            System.out.println("TestConfigProvider uniqueId: " + provider.getUniqueId());
            System.out.println("RedisTestSupport annotation: " + provider.getRedisTestSupport());
        }
    }

    @Test
    void shouldHaveRedisConnectionFactory() {
        // Check if RedisConnectionFactory bean exists
        boolean hasRedisConnectionFactory = applicationContext.containsBean("redisConnectionFactory");
        System.out.println("RedisConnectionFactory bean exists: " + hasRedisConnectionFactory);
        
        if (hasRedisConnectionFactory) {
            RedisConnectionFactory factory = applicationContext.getBean(RedisConnectionFactory.class);
            System.out.println("RedisConnectionFactory type: " + factory.getClass().getName());
        }
    }

    @Test
    void shouldListAllBeans() {
        System.out.println("=== All Redis-related beans ===");
        String[] beanNames = applicationContext.getBeanDefinitionNames();
        for (String beanName : beanNames) {
            if (beanName.toLowerCase().contains("redis") || beanName.toLowerCase().contains("lock")) {
                Object bean = applicationContext.getBean(beanName);
                System.out.println(beanName + " -> " + bean.getClass().getName());
            }
        }
    }
}
