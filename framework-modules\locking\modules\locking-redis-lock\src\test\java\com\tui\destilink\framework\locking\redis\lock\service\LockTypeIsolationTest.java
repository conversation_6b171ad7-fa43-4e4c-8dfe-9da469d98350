package com.tui.destilink.framework.locking.redis.lock.service;

import com.tui.destilink.framework.locking.redis.lock.test.TestApplication;
import com.tui.destilink.framework.test.support.core.util.TestUtils;
import com.tui.destilink.framework.test.support.redis.annotation.RedisTestSupport;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.DirtiesContext;

import java.time.Duration;
import java.util.concurrent.CompletableFuture;

import static org.assertj.core.api.Assertions.*;

/**
 * Test suite for lock-type specific key isolation.
 * <p>
 * Tests ensure that:
 * - Different lock types use different Redis key namespaces
 * - Locks with same logical name but different types don't interfere
 * - State locks and reentrant locks are properly isolated
 * - Key generation follows expected patterns
 * </p>
 */
@RedisTestSupport(keyspacePrefixes = { "test-isolation:" })
@SpringBootTest(classes = TestApplication.class, webEnvironment = SpringBootTest.WebEnvironment.NONE)
@DirtiesContext(classMode = DirtiesContext.ClassMode.BEFORE_EACH_TEST_METHOD)
class LockTypeIsolationTest {

    private static final String UNIQUE_ID = TestUtils.generateTestClassId(LockTypeIsolationTest.class);

    @Autowired
    private RedisLockOperations redisLockOperations;

    @Autowired
    private LockOwnerSupplier lockOwnerSupplier;

    private String baseLockName;
    private String ownerId;
    private Duration leaseTime;

    @BeforeEach
    void setUp() {
        // Use proper hash tag for Redis cluster compatibility and correct prefix for authentication
        baseLockName = UNIQUE_ID + ":test-isolation:{" + UNIQUE_ID + "}:lock";
        ownerId = lockOwnerSupplier.get();
        leaseTime = Duration.ofSeconds(30);
    }

    @Test
    @DisplayName("Should isolate reentrant locks from state locks with same name")
    void shouldIsolateReentrantFromStateLocks() {
        String lockName = baseLockName + ":same-name";
        
        // Acquire as reentrant lock
        String reentrantKey = "reentrant:" + lockName;
        CompletableFuture<Boolean> reentrantAcquired = redisLockOperations.acquireLock(reentrantKey, ownerId, leaseTime);
        assertThat(reentrantAcquired).succeedsWithin(Duration.ofSeconds(5))
                .isEqualTo(true);
        
        // Should be able to acquire as state lock with same logical name (different namespace)
        String stateKey = "state:" + lockName;
        CompletableFuture<Boolean> stateAcquired = redisLockOperations.acquireLock(stateKey, ownerId, leaseTime);
        assertThat(stateAcquired).succeedsWithin(Duration.ofSeconds(5))
                .isEqualTo(true);
        
        // Both should be independently locked
        CompletableFuture<Boolean> reentrantLocked = redisLockOperations.isLocked(reentrantKey);
        CompletableFuture<Boolean> stateLocked = redisLockOperations.isLocked(stateKey);
        
        assertThat(reentrantLocked).succeedsWithin(Duration.ofSeconds(5))
                .isEqualTo(true);
        assertThat(stateLocked).succeedsWithin(Duration.ofSeconds(5))
                .isEqualTo(true);
        
        // Clean up
        redisLockOperations.unlock(reentrantKey, ownerId).join();
        redisLockOperations.unlock(stateKey, ownerId).join();
    }

    @Test
    @DisplayName("Should allow different owners for same lock name in different types")
    void shouldAllowDifferentOwnersForSameLockNameInDifferentTypes() {
        String lockName = baseLockName + ":multi-owner";
        String owner1 = ownerId + ":owner1";
        String owner2 = ownerId + ":owner2";
        
        // Owner1 acquires reentrant lock
        String reentrantKey = "reentrant:" + lockName;
        CompletableFuture<Boolean> reentrantAcquired = redisLockOperations.acquireLock(reentrantKey, owner1, leaseTime);
        assertThat(reentrantAcquired).succeedsWithin(Duration.ofSeconds(5))
                .isEqualTo(true);
        
        // Owner2 should be able to acquire state lock with same logical name
        String stateKey = "state:" + lockName;
        CompletableFuture<Boolean> stateAcquired = redisLockOperations.acquireLock(stateKey, owner2, leaseTime);
        assertThat(stateAcquired).succeedsWithin(Duration.ofSeconds(5))
                .isEqualTo(true);
        
        // Verify both are locked by their respective owners
        CompletableFuture<Boolean> reentrantLocked = redisLockOperations.isLocked(reentrantKey);
        CompletableFuture<Boolean> stateLocked = redisLockOperations.isLocked(stateKey);
        
        assertThat(reentrantLocked).succeedsWithin(Duration.ofSeconds(5))
                .isEqualTo(true);
        assertThat(stateLocked).succeedsWithin(Duration.ofSeconds(5))
                .isEqualTo(true);
        
        // Clean up
        redisLockOperations.unlock(reentrantKey, owner1).join();
        redisLockOperations.unlock(stateKey, owner2).join();
    }

    @Test
    @DisplayName("Should prevent cross-type lock interference")
    void shouldPreventCrossTypeLockInterference() {
        String lockName = baseLockName + ":interference";
        
        // Acquire reentrant lock
        String reentrantKey = "reentrant:" + lockName;
        redisLockOperations.acquireLock(reentrantKey, ownerId, leaseTime).join();
        
        // State lock operations should not affect reentrant lock
        String stateKey = "state:" + lockName;
        redisLockOperations.acquireLock(stateKey, ownerId, leaseTime).join();
        redisLockOperations.unlock(stateKey, ownerId).join();
        
        // Reentrant lock should still be held
        CompletableFuture<Boolean> reentrantStillLocked = redisLockOperations.isLocked(reentrantKey);
        assertThat(reentrantStillLocked).succeedsWithin(Duration.ofSeconds(5))
                .isEqualTo(true);
        
        // Clean up
        redisLockOperations.unlock(reentrantKey, ownerId).join();
    }

    @Test
    @DisplayName("Should handle concurrent operations on different lock types")
    void shouldHandleConcurrentOperationsOnDifferentLockTypes() {
        String lockName = baseLockName + ":concurrent";
        String reentrantKey = "reentrant:" + lockName;
        String stateKey = "state:" + lockName;
        
        // Perform concurrent operations on different lock types
        CompletableFuture<Boolean> reentrantFuture = CompletableFuture.supplyAsync(() -> {
            try {
                return redisLockOperations.acquireLock(reentrantKey, ownerId, leaseTime).get();
            } catch (Exception e) {
                return false;
            }
        });
        
        CompletableFuture<Boolean> stateFuture = CompletableFuture.supplyAsync(() -> {
            try {
                return redisLockOperations.acquireLock(stateKey, ownerId, leaseTime).get();
            } catch (Exception e) {
                return false;
            }
        });
        
        // Both should succeed
        assertThat(reentrantFuture).succeedsWithin(Duration.ofSeconds(5))
                .isEqualTo(true);
        assertThat(stateFuture).succeedsWithin(Duration.ofSeconds(5))
                .isEqualTo(true);
        
        // Clean up
        redisLockOperations.unlock(reentrantKey, ownerId).join();
        redisLockOperations.unlock(stateKey, ownerId).join();
    }

    @Test
    @DisplayName("Should maintain isolation during lock extension")
    void shouldMaintainIsolationDuringLockExtension() {
        String lockName = baseLockName + ":extension";
        String reentrantKey = "reentrant:" + lockName;
        String stateKey = "state:" + lockName;
        
        // Acquire both types
        redisLockOperations.acquireLock(reentrantKey, ownerId, leaseTime).join();
        redisLockOperations.acquireLock(stateKey, ownerId, leaseTime).join();
        
        Duration newLeaseTime = Duration.ofSeconds(60);
        
        // Extend reentrant lock
        CompletableFuture<Boolean> reentrantExtended = redisLockOperations.extendLock(reentrantKey, ownerId, newLeaseTime);
        assertThat(reentrantExtended).succeedsWithin(Duration.ofSeconds(5))
                .isEqualTo(true);
        
        // State lock should still be held with original lease time
        CompletableFuture<Boolean> stateLocked = redisLockOperations.isLocked(stateKey);
        assertThat(stateLocked).succeedsWithin(Duration.ofSeconds(5))
                .isEqualTo(true);
        
        // Extend state lock independently
        CompletableFuture<Boolean> stateExtended = redisLockOperations.extendLock(stateKey, ownerId, newLeaseTime);
        assertThat(stateExtended).succeedsWithin(Duration.ofSeconds(5))
                .isEqualTo(true);
        
        // Both should still be locked
        CompletableFuture<Boolean> reentrantLocked = redisLockOperations.isLocked(reentrantKey);
        assertThat(reentrantLocked).succeedsWithin(Duration.ofSeconds(5))
                .isEqualTo(true);
        assertThat(redisLockOperations.isLocked(stateKey)).succeedsWithin(Duration.ofSeconds(5))
                .isEqualTo(true);
        
        // Clean up
        redisLockOperations.unlock(reentrantKey, ownerId).join();
        redisLockOperations.unlock(stateKey, ownerId).join();
    }

    @Test
    @DisplayName("Should handle complex key patterns with isolation")
    void shouldHandleComplexKeyPatternsWithIsolation() {
        // Test with complex key patterns that might cause collisions
        String complexName = baseLockName + ":complex:with:colons:and-dashes_and_underscores";
        String reentrantKey = "reentrant:" + complexName;
        String stateKey = "state:" + complexName;
        
        // Both should be acquirable independently
        CompletableFuture<Boolean> reentrantAcquired = redisLockOperations.acquireLock(reentrantKey, ownerId, leaseTime);
        CompletableFuture<Boolean> stateAcquired = redisLockOperations.acquireLock(stateKey, ownerId, leaseTime);
        
        assertThat(reentrantAcquired).succeedsWithin(Duration.ofSeconds(5))
                .isEqualTo(true);
        assertThat(stateAcquired).succeedsWithin(Duration.ofSeconds(5))
                .isEqualTo(true);
        
        // Clean up
        redisLockOperations.unlock(reentrantKey, ownerId).join();
        redisLockOperations.unlock(stateKey, ownerId).join();
    }

    @Test
    @DisplayName("Should handle key validation for different lock types")
    void shouldHandleKeyValidationForDifferentLockTypes() {
        // Test that key validation works correctly for both types
        String validName = baseLockName + ":valid";
        String reentrantKey = "reentrant:" + validName;
        String stateKey = "state:" + validName;

        // Both should accept valid keys
        assertThatCode(() -> {
            redisLockOperations.acquireLock(reentrantKey, ownerId, leaseTime).join();
            redisLockOperations.unlock(reentrantKey, ownerId).join();
        }).doesNotThrowAnyException();

        assertThatCode(() -> {
            redisLockOperations.acquireLock(stateKey, ownerId, leaseTime).join();
            redisLockOperations.unlock(stateKey, ownerId).join();
        }).doesNotThrowAnyException();
    }
}
