package com.tui.destilink.framework.locking.redis.lock.service;

import com.tui.destilink.framework.locking.redis.lock.config.RedisLockProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import jakarta.annotation.PreDestroy;

import java.time.Duration;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

/**
 * Service that extends lock leases automatically to prevent premature expiration.
 * <p>
 * The LockWatchdog is always active and conditionally monitors locks based on their
 * safety buffer calculation. It uses PEXPIREAT for precise expiration management
 * and consistently manages expiresAtMillis and originalLeaseTimeMillis.
 * </p>
 * <p>
 * Key features:
 * - Always active (no conditional enablement)
 * - Conditional monitoring based on safety buffer: (expiresAtMillis - currentTimeMillis) <= safetyBuffer
 * - Safety buffer calculated as: originalLeaseTimeMillis * factor
 * - Uses watchdog_refresh_lock.lua script for atomic operations
 * - Preserves originalLeaseTimeMillis (never modifies it)
 * </p>
 */
public class LockWatchdog {

    private static final Logger log = LoggerFactory.getLogger(LockWatchdog.class);

    private final RedisLockOperations lockOperations;
    private final RedisLockErrorHandler errorHandler;
    private final RedisLockProperties.WatchdogProperties watchdogProperties;
    private final ScheduledExecutorService scheduler;
    private final ExecutorService virtualThreadExecutor;
    private final Map<String, WatchedLockInfo> watchedLocks = new ConcurrentHashMap<>();

    /**
     * Creates a new LockWatchdog instance.
     *
     * @param lockOperations     The Redis lock operations service
     * @param errorHandler       The error handler for lock-related errors
     * @param watchdogProperties The configuration properties for the watchdog
     * @param virtualThreadExecutor Virtual Thread executor for async operations
     */
    public LockWatchdog(RedisLockOperations lockOperations,
            RedisLockErrorHandler errorHandler,
            RedisLockProperties.WatchdogProperties watchdogProperties,
            ExecutorService virtualThreadExecutor) {
        this.lockOperations = lockOperations;
        this.errorHandler = errorHandler;
        this.watchdogProperties = watchdogProperties;
        this.virtualThreadExecutor = virtualThreadExecutor;
        // Initialize scheduler here, so watchdogProperties is available
        this.scheduler = Executors.newScheduledThreadPool(
                this.watchdogProperties.getCorePoolSize(),
                r -> {
                    // Use a more robust way to make thread names unique if necessary, e.g.,
                    // AtomicInteger
                    Thread t = new Thread(r,
                            this.watchdogProperties.getThreadNamePrefix() + "pool-" + this.hashCode() + "-thread-");
                    t.setDaemon(true);
                    return t;
                });

        log.info("LockWatchdog initialized with corePoolSize: {}, threadNamePrefix: {}, interval: {}",
                this.watchdogProperties.getCorePoolSize(), this.watchdogProperties.getThreadNamePrefix(),
                this.watchdogProperties.getInterval());
    }

    /**
     * Gets the error handler for lock-related errors.
     *
     * @return The Redis lock error handler.
     */
    public RedisLockErrorHandler getErrorHandler() {
        return errorHandler;
    }

    /**
     * Registers a lock to be monitored by the watchdog.
     *
     * @param lockName      The name of the lock
     * @param lockOwner     The owner identifier of the lock
     * @param leaseDuration The duration of the lock lease
     * @return A handle that can be used to unregister the lock
     */
    public WatchdogHandle registerLock(String lockName, String lockOwner,
            Duration leaseDuration) {
        // The extension interval is now directly from watchdogProperties.getInterval()
        Duration extensionInterval = watchdogProperties.getInterval();

        // Calculate safety buffer: interval * factor
        long safetyBufferMillis = (long) (extensionInterval.toMillis() * watchdogProperties.getFactor());

        // Check if the lease duration is sufficient for watchdog monitoring
        // Only monitor if leaseDuration > safetyBuffer
        if (leaseDuration.toMillis() <= safetyBufferMillis) {
            log.debug("Lock '{}' with owner '{}' not eligible for watchdog monitoring: lease time {}ms <= safety buffer {}ms",
                    lockName, lockOwner, leaseDuration.toMillis(), safetyBufferMillis);
            return () -> {}; // Return no-op handle
        }

        log.debug("Registering lock '{}' with owner '{}' for watchdog monitoring: lease={}ms, safetyBuffer={}ms, interval={}ms",
                lockName, lockOwner, leaseDuration.toMillis(), safetyBufferMillis, extensionInterval.toMillis());

        long currentTimeMillis = System.currentTimeMillis();
        long originalLeaseTimeMillis = leaseDuration.toMillis();
        long expiresAtMillis = currentTimeMillis + originalLeaseTimeMillis;

        WatchedLockInfo watchedLockInfo = new WatchedLockInfo(lockName, lockOwner, originalLeaseTimeMillis, expiresAtMillis);

        // Schedule periodic extension using the interval from WatchdogProperties
        ScheduledFuture<?> future = scheduler.scheduleAtFixedRate(
                () -> conditionalExtendLock(watchedLockInfo),
                extensionInterval.toMillis(), // Initial delay
                extensionInterval.toMillis(), // Period
                TimeUnit.MILLISECONDS);

        watchedLockInfo.setFuture(future);
        watchedLocks.put(createKey(lockName, lockOwner), watchedLockInfo);

        return () -> unregisterLock(lockName, lockOwner);
    }

    /**
     * Registers a lock to be monitored by the watchdog with LockOwnerSupplier.
     *
     * @param lockName      The name of the lock
     * @param lockOwner     The owner identifier of the lock
     * @param leaseDuration The duration of the lock lease
     * @param lockOwnerSupplier The lock owner supplier to check watchdog eligibility
     * @return A handle that can be used to unregister the lock
     */
    public WatchdogHandle registerLock(String lockName, String lockOwner, Duration leaseDuration, LockOwnerSupplier lockOwnerSupplier) {

        // Check if the lock owner supports watchdog monitoring
        if (lockOwnerSupplier != null && !lockOwnerSupplier.canUseWatchdog(lockName, lockOwner)) {
            log.debug("Lock '{}' with owner '{}' not eligible for watchdog monitoring: owner does not support watchdog",
                    lockName, lockOwner);
            return () -> {}; // Return no-op handle
        }

        // Use the existing logic but with the additional check
        return registerLock(lockName, lockOwner, leaseDuration);
    }

    /**
     * Unregisters a lock from watchdog monitoring.
     *
     * @param lockName  The name of the lock
     * @param lockOwner The owner identifier of the lock
     */
    public void unregisterLock(String lockName, String lockOwner) {
        String key = createKey(lockName, lockOwner);
        WatchedLockInfo watchedLockInfo = watchedLocks.remove(key);

        if (watchedLockInfo != null && watchedLockInfo.getFuture() != null) {
            watchedLockInfo.getFuture().cancel(false);
            log.debug("Unregistered lock '{}' with owner '{}' from watchdog monitoring",
                    lockName, lockOwner);
        }
    }

    /**
     * Conditionally extends the lease of a watched lock based on safety buffer calculations.
     * Only extends if the remaining time is within the safety buffer threshold.
     * Uses the watchdog_refresh_lock.lua script for atomic operations.
     *
     * @param watchedLockInfo The lock to potentially extend
     */
    private void conditionalExtendLock(WatchedLockInfo watchedLockInfo) {
        long currentTimeMillis = System.currentTimeMillis();
        double factor = watchdogProperties.getFactor();

        // Check if the lock is eligible for monitoring based on safety buffer
        if (!watchedLockInfo.isEligibleForMonitoring(currentTimeMillis, factor)) {
            log.trace("Lock '{}' with owner '{}' not yet eligible for extension - remaining time > safety buffer",
                    watchedLockInfo.getLockName(), watchedLockInfo.getLockOwner());
            return;
        }

        // Calculate new target expiration time (extend by safety buffer)
        long safetyBufferMillis = (long) (watchdogProperties.getInterval().toMillis() * factor);
        long targetExpiresAtMillis = currentTimeMillis + safetyBufferMillis;

        // Use the watchdog refresh script for atomic extension
        extendLockWithWatchdogScript(watchedLockInfo, targetExpiresAtMillis);
    }

    /**
     * Extends the lease of a watched lock using the watchdog_refresh_lock.lua script.
     * This method uses the dedicated watchdog script that preserves originalLeaseTimeMillis.
     *
     * @param watchedLockInfo The lock to extend
     * @param targetExpiresAtMillis The target expiration time in milliseconds
     */
    private void extendLockWithWatchdogScript(WatchedLockInfo watchedLockInfo, long targetExpiresAtMillis) {
        // Use the watchdog refresh script which preserves originalLeaseTimeMillis
        lockOperations.refreshLockByWatchdog(
                watchedLockInfo.getLockName(),
                watchedLockInfo.getLockOwner(),
                targetExpiresAtMillis)
        .thenAccept(result -> {
            if (result != null && result > 0) {
                log.debug("Watchdog extended lock '{}' with owner '{}' to expire at {}",
                        watchedLockInfo.getLockName(), watchedLockInfo.getLockOwner(), targetExpiresAtMillis);

                // Update the tracked expiration time
                watchedLockInfo.setExpiresAtMillis(targetExpiresAtMillis);
            } else {
                // If not extended, it might mean the lock was lost or owner changed.
                log.warn(
                        "Watchdog failed to extend lock '{}' with owner '{}' - lock may have been lost or owner changed.",
                        watchedLockInfo.getLockName(), watchedLockInfo.getLockOwner());
                unregisterLock(watchedLockInfo.getLockName(), watchedLockInfo.getLockOwner());
            }
        }).exceptionally(throwable -> {
            log.error("Watchdog exception during lock extension for '{}', owner '{}': {}",
                    watchedLockInfo.getLockName(), watchedLockInfo.getLockOwner(), throwable.getMessage());

            // Convert Throwable to Exception for error handler
            Exception exception = (throwable instanceof Exception) ? (Exception) throwable : new RuntimeException(throwable);
            errorHandler.handleLockExtensionError(watchedLockInfo.getLockName(), exception, watchedLockInfo.getLockOwner());

            // Remove from watched locks since extension failed definitively
            unregisterLock(watchedLockInfo.getLockName(), watchedLockInfo.getLockOwner());
            return null;
        });
    }

    private String createKey(String lockName, String lockOwner) {
        return lockName + ":" + lockOwner;
    }

    /**
     * Shuts down the watchdog, canceling all scheduled tasks.
     */
    @PreDestroy
    public void shutdown() {
        log.info("Shutting down LockWatchdog");
        scheduler.shutdown();
        try {
            if (!scheduler.awaitTermination(watchdogProperties.getShutdownAwaitTermination().toMillis(),
                    TimeUnit.MILLISECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            scheduler.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }

    /**
     * Functional interface for watchdog handle that can be used to unregister a
     * lock.
     */
    @FunctionalInterface
    public interface WatchdogHandle {
        /**
         * Unregisters the lock from watchdog monitoring.
         */
        void unregister();
    }

    /**
     * Represents a lock being watched by the watchdog with precise timing information.
     */
    private static class WatchedLockInfo {
        private final String lockName;
        private final String lockOwner;
        private final long originalLeaseTimeMillis;
        private volatile long expiresAtMillis;
        private volatile ScheduledFuture<?> future;

        public WatchedLockInfo(String lockName, String lockOwner, long originalLeaseTimeMillis, long expiresAtMillis) {
            this.lockName = lockName;
            this.lockOwner = lockOwner;
            this.originalLeaseTimeMillis = originalLeaseTimeMillis;
            this.expiresAtMillis = expiresAtMillis;
        }

        public String getLockName() {
            return lockName;
        }

        public String getLockOwner() {
            return lockOwner;
        }

        public long getOriginalLeaseTimeMillis() {
            return originalLeaseTimeMillis;
        }

        public long getExpiresAtMillis() {
            return expiresAtMillis;
        }

        public void setExpiresAtMillis(long expiresAtMillis) {
            this.expiresAtMillis = expiresAtMillis;
        }

        public ScheduledFuture<?> getFuture() {
            return future;
        }

        public void setFuture(ScheduledFuture<?> future) {
            this.future = future;
        }

        /**
         * Checks if this lock is eligible for watchdog monitoring based on the safety buffer.
         * @param currentTimeMillis Current time in milliseconds
         * @param factor Safety buffer factor from configuration
         * @return true if the lock is eligible for monitoring
         */
        public boolean isEligibleForMonitoring(long currentTimeMillis, double factor) {
            long remainingTimeMillis = expiresAtMillis - currentTimeMillis;
            long safetyBufferMillis = (long) (originalLeaseTimeMillis * factor);
            return remainingTimeMillis <= safetyBufferMillis;
        }
    }
}